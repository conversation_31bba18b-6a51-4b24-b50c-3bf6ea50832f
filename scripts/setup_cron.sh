#!/bin/bash

# Get the absolute path of the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Create the cron job entry
CRON_JOB="*/5 * * * * php $SCRIPT_DIR/process_reminders.php >> $SCRIPT_DIR/../logs/cron.log 2>&1"

# Add the cron job to the current user's crontab
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

echo "Cron job has been set up successfully!"
echo "The reminder processor will run every 5 minutes."
echo "Logs will be written to $SCRIPT_DIR/../logs/cron.log" 
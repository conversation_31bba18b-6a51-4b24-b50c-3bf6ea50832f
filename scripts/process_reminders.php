<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../api/appointment_reminder.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set maximum execution time to 5 minutes
set_time_limit(300);

// Initialize the reminder processor
$reminder = new AppointmentReminder($pdo);

// Process reminders
$result = $reminder->processReminders();

// Log the result
$logMessage = date('Y-m-d H:i:s') . " - Reminder processing " . ($result ? "completed successfully" : "failed") . "\n";
file_put_contents(__DIR__ . '/../logs/reminder_processing.log', $logMessage, FILE_APPEND);

// If running from command line, output the result
if (php_sapi_name() === 'cli') {
    echo $logMessage;
} 
<?php
require_once '../config/config.php';
require_once '../includes/helpers.php';
require_once '../auth/auth_middleware.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
checkAuth();

$user_id = $_SESSION['user_id'];

// Fetch user's appointments
$stmt = $pdo->prepare("
    SELECT 
        a.*,
        u.first_name,
        u.last_name,
        tp.specialization,
        COALESCE(tp.profile_image, u.profile_image, '../assets/images/default-avatar.png') as profile_image
    FROM appointments a 
    JOIN users u ON a.therapist_id = u.id
    LEFT JOIN therapist_profiles tp ON u.id = tp.user_id
    WHERE a.user_id = ? 
    ORDER BY a.appointment_date DESC
");
$stmt->execute([$user_id]);
$appointments = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointments - MindCare</title>
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/appointments.css">
    <link rel="stylesheet" href="../assets/css/sidebar.css">
    <link rel="stylesheet" href="../assets/css/modal.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .therapist-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .therapist-card {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .therapist-card img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 10px;
        }
        .therapist-card h4 {
            margin: 10px 0 5px;
            color: var(--text-primary);
        }
        .therapist-card .specialization {
            font-size: 0.9em;
            color: var(--text-secondary);
            margin-bottom: 15px;
        }
        #appointment-form {
            margin-top: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-primary);
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--input-bg);
            color: var(--text-primary);
        }
        
        /* Custom modal styling */
        #paymentModal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            z-index: 9999;
            overflow: auto;
        }
        
        #paymentModal .modal-content {
            background-color: var(--card-bg);
            margin: 10% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            position: relative;
        }
        
        #paymentModal .close {
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        #paymentModal h2 {
            margin-top: 0;
            color: var(--text-primary);
        }
        
        #paymentDetails {
            margin-bottom: 20px;
            padding: 10px;
            background-color: var(--bg-light);
            border-radius: 4px;
        }
    </style>
</head>
<body>
<button class="theme-toggle" aria-label="Toggle theme" style="position: fixed; top: 20px; right: 20px; z-index: 1000; width: 21px; height: 21px; border-radius: 50%; font-size: 24px; padding: 0; display: flex; align-items: center; justify-content: center;">🌙</button>

    <div class="dashboard-layout">
        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle" aria-label="Toggle menu">
            <i class="fas fa-bars"></i>
        </button>
        <?php include '../includes/sidebar.php'; ?>

        <main class="main-content">
            <div class="appointments-container">
                <div class="page-header">
                    <h1>My Appointments</h1>
                    <button class="btn-primary" id="openBookingModal">
                        <i class="fas fa-plus"></i> Book New Appointment
                    </button>
                    
                    <div id="bookingModal" class="modal">
                        <div class="modal-content">
                            <span class="close">&times;</span>
                            <div id="bookingFormContainer"></div>
                        </div>
                    </div>
                </div>

                <div class="appointments-grid">
                    <?php foreach ($appointments as $appointment): ?>
                        <div class="appointment-card">
                            <div class="therapist-info">
                                <img src="<?php echo htmlspecialchars($appointment['profile_image']); ?>" alt="Therapist" class="therapist-avatar">
                                <div>
                                    <h3><?php echo htmlspecialchars($appointment['first_name'] . ' ' . $appointment['last_name']); ?></h3>
                                    <p class="specialization"><?php echo htmlspecialchars($appointment['specialization']); ?></p>
                                </div>
                            </div>
                            <div class="appointment-details">
                                <p><i class="fas fa-calendar"></i> <?php echo date('F j, Y', strtotime($appointment['appointment_date'])); ?></p>
                                <p><i class="fas fa-clock"></i> <?php echo date('g:i A', strtotime($appointment['appointment_date'])); ?></p>
                                <p><i class="fas fa-info-circle"></i> Status: <span class="status-<?php echo strtolower($appointment['status']); ?>"><?php echo ucfirst($appointment['status']); ?></span></p>
                            </div>
                            <div class="appointment-actions">
                                <?php if ($appointment['status'] == 'scheduled'): ?>
                                    <button class="btn-secondary" onclick="window.location.href='messages.php?therapist_id=<?php echo $appointment["therapist_id"]; ?>'">
                                        <i class="fas fa-comments"></i> Message
                                    </button>
                                    <button class="btn-danger" onclick="cancelAppointment(<?php echo $appointment['id']; ?>)">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                    <!-- Simple payment link instead of button with JavaScript -->
                                    <a href="payment.php?id=<?php echo $appointment['id']; ?>" class="btn-primary" style="display: inline-block; text-decoration: none;">
                                        <i class="fas fa-credit-card"></i> Pay Now
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </main>
    </div>

    <link rel="stylesheet" href="../assets/css/modal.css">
    <script src="../assets/js/modal.js" defer></script>
    <script src="../assets/js/theme.js"></script>
    
    <!-- Payment Modal -->
    <div id="paymentModal" class="modal" style="z-index: 2000;">
        <div class="modal-content">
            <span class="close" onclick="closePaymentModal()">&times;</span>
            <h2>Pay for Session</h2>
            <div id="paymentDetails">
                <p>Therapist: <span id="therapistName"></span></p>
                <p>Amount: KSH <span id="paymentAmount"></span></p>
            </div>
            <form id="mpesaPaymentForm" action="../api/process_appointment_payment.php" method="POST">
                <input type="hidden" id="appointmentId" name="appointment_id">
                <input type="hidden" id="amount" name="amount">
                
                <div class="form-group">
                    <label for="phone">M-Pesa Phone Number</label>
                    <input type="tel" id="phone" name="phone" pattern="^254[0-9]{9}$" 
                           placeholder="254XXXXXXXXX" required
                           title="Please enter a valid Kenyan phone number starting with 254">
                    <small>Enter your M-Pesa number starting with 254</small>
                </div>
                
                <div class="payment-info">
                    <p>You will receive an M-Pesa prompt on your phone to complete the payment.</p>
                    <p>Please ensure:</p>
                    <ul>
                        <li>Your phone number is correct</li>
                        <li>You have sufficient funds in your M-Pesa account</li>
                        <li>Keep your phone ready for the payment prompt</li>
                    </ul>
                </div>
                
                <button type="submit" class="btn-primary">Pay with M-Pesa</button>
            </form>
        </div>
    </div>
    
    <script>
        // Add debug logging for modal
        console.log('Payment modal exists:', document.getElementById('paymentModal') !== null);
        
        function closePaymentModal() {
            console.log('Closing payment modal');
            document.getElementById('paymentModal').style.display = 'none';
        }
        
        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('paymentModal');
            if (event.target == modal) {
                closePaymentModal();
            }
        }
        
        function cancelAppointment(appointmentId) {
            if (confirm('Are you sure you want to cancel this appointment?')) {
                // Send request to cancel the appointment
                fetch('../api/cancel_appointment.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        appointment_id: appointmentId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Appointment cancelled successfully.');
                        window.location.reload();
                    } else {
                        alert('Failed to cancel appointment: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while cancelling the appointment.');
                });
            }
        }
    </script>
</body>
</html>
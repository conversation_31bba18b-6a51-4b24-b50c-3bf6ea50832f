<?php
require_once __DIR__ . '/../auth/auth_middleware.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../api/notification_handler.php';

// Check if user is logged in
checkAuth();

// Check if this is an AJAX request
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
          strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

// Handle form submission via AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $isAjax) {
    header('Content-Type: application/json');
    
    try {
        $stmt = $pdo->prepare("INSERT INTO appointments 
                              (user_id, therapist_id, appointment_date, duration, notes, meeting_link) 
                              VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $_SESSION['user_id'],
            $_POST['therapist_id'],
            $_POST['appointment_date'],
            $_POST['duration'],
            $_POST['notes'] ?? '',
            $_POST['meeting_preference'] === 'virtual' ? 'Zoom link will be provided' : 'In-person session'
        ]);
        
        // Get the last inserted appointment ID
        $appointmentId = $pdo->lastInsertId();
        
        // Create notification for the therapist
        $notificationHandler = new NotificationHandler($pdo);
        $stmt = $pdo->prepare("SELECT first_name, last_name FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $message = "New appointment request from {$user['first_name']} {$user['last_name']} for {$_POST['appointment_date']}";
        $notificationHandler->createNotification($_POST['therapist_id'], 'appointment_request', $message, $appointmentId);
        
        echo json_encode(['success' => true]);
        exit();
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => "Error booking appointment: " . $e->getMessage()]);
        exit();
    }
}

// Get available therapists
$therapists = [];
$stmt = $pdo->prepare("SELECT u.id, u.first_name, u.last_name, t.specialization, t.hourly_rate 
                      FROM users u 
                      JOIN therapist_profiles t ON u.id = t.user_id 
                      WHERE u.role = 'therapist' AND u.active = TRUE");
$stmt->execute();
$therapists = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle form submission for direct visits (non-AJAX)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$isAjax) {
    $therapistId = $_POST['therapist_id'];
    $appointmentDate = $_POST['appointment_date'];
    $duration = $_POST['duration'];
    $notes = $_POST['notes'] ?? '';
    $meetingPreference = $_POST['meeting_preference'];
    
    try {
        $stmt = $pdo->prepare("INSERT INTO appointments 
                              (user_id, therapist_id, appointment_date, duration, notes, meeting_link) 
                              VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $_SESSION['user_id'],
            $therapistId,
            $appointmentDate,
            $duration,
            $notes,
            $meetingPreference === 'virtual' ? 'Zoom link will be provided' : 'In-person session'
        ]);
        
        // Get the last inserted appointment ID
        $appointmentId = $pdo->lastInsertId();
        
        // Create notification for the therapist
        $notificationHandler = new NotificationHandler($pdo);
        $stmt = $pdo->prepare("SELECT first_name, last_name FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $message = "New appointment request from {$user['first_name']} {$user['last_name']} for {$appointmentDate}";
        $notificationHandler->createNotification($therapistId, 'appointment_request', $message, $appointmentId);
        
        $_SESSION['success_message'] = "Appointment booked successfully!";
        header("Location: appointments.php");
        exit();
    } catch (PDOException $e) {
        $_SESSION['error_message'] = "Error booking appointment: " . $e->getMessage();
    }
}

// Only output HTML if this is not an AJAX request
if (!$isAjax) {
?>
<div class="modal-booking-form">
    <div class="container">
        <h1>Book New Appointment</h1>
            
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger"><?= $_SESSION['error_message'] ?></div>
            <?php unset($_SESSION['error_message']); ?>
        <?php endif; ?>
            
        <form method="POST" class="appointment-form">
            <div class="form-group">
                <label for="therapist_id">Select Therapist</label>
                <select name="therapist_id" id="therapist_id" required>
                    <option value="">-- Select Therapist --</option>
                    <?php foreach ($therapists as $therapist): ?>
                        <option value="<?= $therapist['id'] ?>">
                            <?= htmlspecialchars($therapist['first_name'] . ' ' . $therapist['last_name']) ?> 
                            (<?= htmlspecialchars($therapist['specialization']) ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
                
            <div class="form-group">
                <label for="appointment_date">Date & Time</label>
                <input type="datetime-local" name="appointment_date" id="appointment_date" required>
            </div>
                
            <div class="form-group">
                <label for="duration">Duration (minutes)</label>
                <select name="duration" id="duration" required>
                    <option value="30">30 minutes</option>
                    <option value="60" selected>60 minutes</option>
                    <option value="90">90 minutes</option>
                </select>
            </div>
                
            <div class="form-group">
                <label>Meeting Preference</label>
                <div class="radio-group">
                    <label>
                        <input type="radio" name="meeting_preference" value="virtual" checked> Virtual
                    </label>
                    <label>
                        <input type="radio" name="meeting_preference" value="in_person"> In-Person
                    </label>
                </div>
            </div>
                
            <div class="form-group">
                <label for="notes">Notes (Optional)</label>
                <textarea name="notes" id="notes" rows="3"></textarea>
            </div>
                
            <button type="submit" class="btn btn-primary">Book Appointment</button>
        </form>
    </div>
</div>
<?php
}
?>
<?php
require_once __DIR__ . '/../config/email_config.php';
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

function sendEmail($to, $subject, $body, $attachments = []) {
    if (!validateEmailConfig()) {
        logEmailError('Cannot send email - invalid configuration');
        return false;
    }

    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_ENCRYPTION;
        $mail->Port = SMTP_PORT;

        // Recipients
        $mail->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        $mail->addAddress($to);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;

        // Attachments
        foreach ($attachments as $attachment) {
            if (isset($attachment['path']) && isset($attachment['name'])) {
                $mail->addAttachment($attachment['path'], $attachment['name']);
            }
        }

        $mail->send();
        return true;
    } catch (Exception $e) {
        logEmailError($e->getMessage(), [
            'to' => $to,
            'subject' => $subject,
            'error_code' => $e->getCode(),
            'error_line' => $e->getLine(),
            'error_file' => $e->getFile()
        ]);
        return false;
    }
}

function sendTherapistApprovalEmail($therapistEmail, $therapistName, $isApproved) {
    $subject = $isApproved ? 
        'Your Therapist Application has been Approved' : 
        'Update on Your Therapist Application';
    
    $body = $isApproved ? 
        "Dear {$therapistName},\n\n" .
        "We are pleased to inform you that your application to become a therapist on MindCare has been approved. " .
        "You can now log in to your account and start helping others.\n\n" .
        "Best regards,\nThe MindCare Team" :
        "Dear {$therapistName},\n\n" .
        "Thank you for your interest in becoming a therapist on MindCare. " .
        "After careful review of your application, we regret to inform you that we are unable to approve your application at this time.\n\n" .
        "Best regards,\nThe MindCare Team";

    return sendEmail($therapistEmail, $subject, $body);
}

function sendPasswordResetEmail($userEmail, $resetToken) {
    $resetLink = SITE_URL . "/reset_password.php?token=" . urlencode($resetToken);
    
    $subject = 'Password Reset Request - MindCare';
    $body = "Hello,\n\n" .
            "You have requested to reset your password. Click the link below to proceed:\n\n" .
            "<a href='{$resetLink}'>{$resetLink}</a>\n\n" .
            "If you did not request this, please ignore this email.\n\n" .
            "Best regards,\nThe MindCare Team";

    return sendEmail($userEmail, $subject, $body);
} 
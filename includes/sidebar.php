<aside class="sidebar">
    <nav>
        <a href="../user/dashboard.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
            <i class="fas fa-home"></i> Home
        </a>
        <a href="../user/appointments.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'appointments.php' ? 'active' : ''; ?>">
            <i class="fas fa-calendar-alt"></i> Appointments
        </a>
        <a href="../user/messages.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'messages.php' ? 'active' : ''; ?>">
            <i class="fas fa-comments"></i> Messages
        </a>
        <a href="../user/journal.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'journal.php' ? 'active' : ''; ?>">
            <i class="fas fa-book"></i> Journal
        </a>
        <a href="../user/resources.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'resources.php' ? 'active' : ''; ?>">
            <i class="fas fa-book-reader"></i> Resources
        </a>
        <a href="../user/notifications.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'notifications.php' ? 'active' : ''; ?>">
            <i class="fas fa-bell"></i> Notifications
        </a>
    </nav>
    <div class="sidebar-footer">
        <a href="../user/profile.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'profile.php' ? 'active' : ''; ?>">
            <i class="fas fa-user"></i> Profile
        </a>
        <a href="../user/settings.php" class="nav-item <?php echo $current_page == 'settings.php' ? 'active' : ''; ?>">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
            <a href="../user/payments.php" class="nav-item <?php echo $current_page == 'payments.php' ? 'active' : ''; ?>">
                <i class="fas fa-credit-card"></i>
                <span>Payments</span>
            </a>
        <a href="../user/help-center.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) == 'help-center.php' ? 'active' : ''; ?>">
            <i class="fas fa-question-circle"></i> Help Center
        </a>
        <a href="../auth/logout.php" class="nav-item logout-btn" onclick="return confirm('Are you sure you want to logout?');">
            <i class="fas fa-sign-out-alt"></i> Sign Out
        </a>
    </div>
</aside>

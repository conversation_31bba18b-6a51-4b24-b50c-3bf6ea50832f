<?php
function getMoodEmoji($score) {
    switch ($score) {
        case 1:
            return '😔';
        case 2:
            return '😕';
        case 3:
            return '😊';
        case 4:
            return '😃';
        case 5:
            return '🤗';
        default:
            return '😐';
    }
}

function getUserData($user_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            return null;
        }
        
        // Remove sensitive information
        unset($user['password']);
        
        return $user;
    } catch (PDOException $e) {
        error_log('Error fetching user data: ' . $e->getMessage());
        return null;
    }
}
?>
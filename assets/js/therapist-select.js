// Therapist Selection Modal Handler
class TherapistSelector {
    constructor() {
        this.modal = null;
        this.selectedTherapistId = null;
        this.specializations = [];
        this.therapists = [];
        this.searchTimeout = null;
        this.initialize();
    }

    initialize() {
        this.createModal();
        this.bindEvents();
        this.loadSpecializations();
    }

    createModal() {
        const modalHtml = `
            <div class="therapist-select-modal" id="therapistSelectModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Select a Therapist</h3>
                        <button class="close-modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="search-filters">
                            <input type="text" id="therapistSearch" placeholder="Search therapists..." class="search-input">
                            <select id="specializationFilter" class="specialization-filter">
                                <option value="">All Specializations</option>
                            </select>
                        </div>
                        <div class="therapists-list" id="therapistsList"></div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        this.modal = document.getElementById('therapistSelectModal');
    }

    bindEvents() {
        // Close modal
        this.modal.querySelector('.close-modal').addEventListener('click', () => this.hideModal());

        // Search input
        const searchInput = document.getElementById('therapistSearch');
        searchInput.addEventListener('input', () => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => this.filterTherapists(), 300);
        });

        // Specialization filter
        document.getElementById('specializationFilter').addEventListener('change', () => this.filterTherapists());
    }

    async loadSpecializations() {
        try {
            const response = await fetch('../api/get_specializations.php');
            const data = await response.json();
            if (data.success) {
                this.specializations = data.specializations;
                this.populateSpecializationFilter();
            }
        } catch (error) {
            console.error('Error loading specializations:', error);
        }
    }

    populateSpecializationFilter() {
        const select = document.getElementById('specializationFilter');
        this.specializations.forEach(spec => {
            const option = document.createElement('option');
            option.value = spec.id;
            option.textContent = spec.name;
            select.appendChild(option);
        });
    }

    async loadTherapists() {
        try {
            const response = await fetch('../api/get_available_therapists.php');
            const data = await response.json();
            if (data.success) {
                this.therapists = data.therapists;
                this.renderTherapists();
            }
        } catch (error) {
            console.error('Error loading therapists:', error);
        }
    }

    filterTherapists() {
        const searchTerm = document.getElementById('therapistSearch').value.toLowerCase();
        const specialization = document.getElementById('specializationFilter').value;

        const filtered = this.therapists.filter(therapist => {
            const matchesSearch = therapist.name.toLowerCase().includes(searchTerm);
            const matchesSpecialization = !specialization || therapist.specialization_id === specialization;
            return matchesSearch && matchesSpecialization;
        });

        this.renderTherapists(filtered);
    }

    renderTherapists(therapists = this.therapists) {
        const list = document.getElementById('therapistsList');
        list.innerHTML = '';

        therapists.forEach(therapist => {
            const therapistCard = document.createElement('div');
            therapistCard.className = 'therapist-card';
            therapistCard.innerHTML = `
                <img src="${therapist.profile_image || '../assets/images/default-avatar.png'}" alt="${therapist.first_name} ${therapist.last_name}" class="therapist-avatar">
                <div class="therapist-info">
                    <h4>${therapist.first_name} ${therapist.last_name}</h4>
                    <p class="specialization">${therapist.specialization}</p>
                    <p class="experience">${therapist.experience_years} years experience</p>
                    <p class="languages">Languages: ${therapist.languages_spoken}</p>
                    <p class="bio">${therapist.bio}</p>
                    <div class="qualification">
                        <p><strong>Qualification:</strong> ${therapist.degree} from ${therapist.institution}</p>
                        <p><strong>License Number:</strong> ${therapist.license_number}</p>
                    </div>
                </div>
                <button class="view-profile-btn" data-therapist-id="${therapist.id}">View Profile</button>
                <button class="start-chat-btn" data-therapist-id="${therapist.id}">Start Chat</button>
            `;

            therapistCard.querySelector('.start-chat-btn').addEventListener('click', () => {
                this.selectedTherapistId = therapist.id;
                this.startChat();
            });

            list.appendChild(therapistCard);
        });
    }

    async startChat() {
        if (!this.selectedTherapistId) return;

        try {
            const response = await fetch('../api/create_chat.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    therapist_id: this.selectedTherapistId
                })
            });

            const data = await response.json();
            if (data.success) {
                window.location.reload(); // Reload to show new chat
            } else {
                alert('Failed to start chat. Please try again.');
            }
        } catch (error) {
            console.error('Error starting chat:', error);
            alert('An error occurred. Please try again.');
        }
    }

    showModal() {
        this.modal.style.display = 'flex';
        this.loadTherapists();
    }

    hideModal() {
        this.modal.style.display = 'none';
    }
}

// Initialize the therapist selector
const therapistSelector = new TherapistSelector();

// Export for use in other files
window.therapistSelector = therapistSelector;
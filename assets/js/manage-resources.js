class ResourceManager {
    constructor() {
        this.modal = document.getElementById('resourceModal');
        this.form = document.getElementById('resourceForm');
        this.createBtn = document.getElementById('createResourceBtn');
        this.closeBtn = this.modal.querySelector('.close-modal');
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Create resource button
        this.createBtn.addEventListener('click', () => this.openModal());
        
        // Close modal button
        this.closeBtn.addEventListener('click', () => this.closeModal());
        
        // Close modal when clicking outside
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });
        
        // Form submission
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
    }
    
    openModal(resourceId = null) {
        const header = this.modal.querySelector('.modal-header h2');
        header.textContent = resourceId ? 'Edit Resource' : 'Create New Resource';
        
        if (resourceId) {
            this.loadResource(resourceId);
        } else {
            this.form.reset();
        }
        
        this.modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
    
    closeModal() {
        this.modal.style.display = 'none';
        document.body.style.overflow = '';
        this.form.reset();
    }
    
    async loadResource(resourceId) {
        try {
            const response = await fetch(`/api/resources/${resourceId}`);
            const resource = await response.json();
            
            // Populate form fields
            document.getElementById('resourceId').value = resource.id;
            document.getElementById('title').value = resource.title;
            document.getElementById('description').value = resource.description;
            document.getElementById('content').value = resource.content;
            document.getElementById('category').value = resource.category;
            document.getElementById('icon').value = resource.icon;
            document.getElementById('duration').value = resource.duration;
            
            // Load tags
            const tags = await this.loadResourceTags(resourceId);
            document.getElementById('tags').value = tags.join(', ');
            
        } catch (error) {
            console.error('Error loading resource:', error);
            this.showError('Failed to load resource. Please try again.');
        }
    }
    
    async loadResourceTags(resourceId) {
        try {
            const response = await fetch(`/api/resources/${resourceId}/tags`);
            const tags = await response.json();
            return tags.map(tag => tag.tag_name);
        } catch (error) {
            console.error('Error loading tags:', error);
            return [];
        }
    }
    
    async handleSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(this.form);
        const resourceId = formData.get('resource_id');
        
        try {
            const response = await fetch('/api/resources' + (resourceId ? `/${resourceId}` : ''), {
                method: resourceId ? 'PUT' : 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error('Failed to save resource');
            }
            
            const result = await response.json();
            
            // Refresh the page to show updated resources
            window.location.reload();
            
        } catch (error) {
            console.error('Error saving resource:', error);
            this.showError('Failed to save resource. Please try again.');
        }
    }
    
    async deleteResource(resourceId) {
        if (!confirm('Are you sure you want to delete this resource?')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/resources/${resourceId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                throw new Error('Failed to delete resource');
            }
            
            // Remove the resource card from the UI
            const card = document.querySelector(`.resource-card[data-id="${resourceId}"]`);
            if (card) {
                card.remove();
            }
            
        } catch (error) {
            console.error('Error deleting resource:', error);
            this.showError('Failed to delete resource. Please try again.');
        }
    }
    
    showError(message) {
        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        // Remove notification after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize ResourceManager when the document is ready
document.addEventListener('DOMContentLoaded', () => {
    window.resourceManager = new ResourceManager();
});

// Global functions for button clicks
function editResource(resourceId) {
    window.resourceManager.openModal(resourceId);
}

function deleteResource(resourceId) {
    window.resourceManager.deleteResource(resourceId);
}

function closeModal() {
    window.resourceManager.closeModal();
} 
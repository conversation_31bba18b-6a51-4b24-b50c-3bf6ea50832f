document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('bookingModal');
    const btn = document.getElementById('openBookingModal');
    const span = document.querySelector('.close');
    const bookingFormContainer = document.getElementById('bookingFormContainer');

    if (btn) {
        btn.onclick = function() {
            fetch('book-appointment.php', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                bookingFormContainer.innerHTML = html;
                modal.style.display = 'block';
                
                // Handle form submission
                const form = bookingFormContainer.querySelector('form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        fetch('book-appointment.php', {
                            method: 'POST',
                            body: new FormData(form),
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                window.location.reload();
                            } else {
                                alert(data.message || 'Error booking appointment');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred while booking the appointment');
                        });
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                modal.style.display = 'block';
                bookingFormContainer.innerHTML = '<p>Error loading booking form</p>';
            });
        };
    }

    if (span) {
        span.onclick = function() {
            modal.style.display = 'none';
        };
    }

    window.onclick = function(event) {
        if (event.target == modal) {
            modal.style.display = 'none';
        }
    };
});
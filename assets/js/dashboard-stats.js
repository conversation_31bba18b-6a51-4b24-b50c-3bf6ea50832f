// Dashboard statistics and goals management
document.addEventListener('DOMContentLoaded', function() {
    // Fetch weekly goals
    function fetchWeeklyGoals() {
        fetch('../api/weekly-goals.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'list'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.goals) {
                updateGoalsDisplay(data.goals);
            }
        })
        .catch(error => console.error('Error:', error));
    }

    // Update goals display
    function updateGoalsDisplay(goals) {
        const statElement = document.querySelector('.stat-card .fa-tasks');
        
        // Early return if goals stat card is not present in this dashboard
        if (!statElement) {
            console.debug('Goals stat card not found - this is expected for some dashboard types');
            return;
        }

        const statNumberElement = statElement.closest('.stat-card')?.querySelector('.stat-number');
        if (!statNumberElement) {
            console.debug('Goals stat number element not found');
            return;
        }

        const completedGoals = goals.filter(goal => goal.status === 'completed').length;
        const totalGoals = goals.length;
        statNumberElement.textContent = `${completedGoals} of ${totalGoals} Completed`;
    }

    // Add new goal
    window.addNewGoal = function(description) {
        fetch('../api/weekly-goals.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'add',
                description: description
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                fetchWeeklyGoals();
            }
        })
        .catch(error => console.error('Error:', error));
    };

    // Update goal status
    window.updateGoalStatus = function(goalId, status) {
        fetch('../api/weekly-goals.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'update',
                goal_id: goalId,
                status: status
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                fetchWeeklyGoals();
            }
        })
        .catch(error => console.error('Error:', error));
    };

    // Initial fetch
    fetchWeeklyGoals();
});
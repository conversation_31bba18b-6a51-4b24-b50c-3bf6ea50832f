document.addEventListener('DOMContentLoaded', function() {
    // Modal Handling
    const modal = document.getElementById('appointmentModal');
    const confirmationModal = document.getElementById('confirmationModal');
    const openModalBtn = document.getElementById('openBookingModal');
    const closeButtons = document.querySelectorAll('.close');
    const closeConfirmationBtn = document.getElementById('closeConfirmation');

    // Elements for booking process
    const therapistGrid = document.getElementById('therapistGrid');
    const appointmentTypes = document.getElementById('appointmentTypes');
    const calendarGrid = document.getElementById('calendarGrid');
    const timeSlots = document.getElementById('timeSlots');
    const bookingBtn = document.getElementById('bookAppointment');
    
    // Calendar navigation elements
    const prevMonthBtn = document.getElementById('prevMonth');
    const nextMonthBtn = document.getElementById('nextMonth');
    const currentMonthDisplay = document.getElementById('currentMonth');
    
    // Booking summary elements
    const summaryTherapist = document.getElementById('summaryTherapist');
    const summaryType = document.getElementById('summaryType');
    const summaryDate = document.getElementById('summaryDate');
    const summaryTime = document.getElementById('summaryTime');
    const summaryPrice = document.getElementById('summaryPrice');
    
    // Booking state
    let bookingState = {
        therapist: null,
        appointmentType: null,
        date: null,
        time: null,
        price: 0
    };
    
    // Sample therapists data (would normally come from an API/backend)
    const therapists = [
        {
            id: 1,
            name: "Dr. Sarah Kamau",
            specialty: "Depression & Anxiety",
            image: "assets/images/therapist1.jpg",
            availability: ["Monday", "Wednesday", "Friday"]
        },
        {
            id: 2,
            name: "Dr. James Omondi",
            specialty: "Family Therapy",
            image: "assets/images/therapist2.jpg",
            availability: ["Tuesday", "Thursday", "Saturday"]
        },
        {
            id: 3,
            name: "Dr. Lucy Wambui",
            specialty: "Trauma & PTSD",
            image: "assets/images/therapist3.jpg",
            availability: ["Monday", "Tuesday", "Thursday"]
        },
        {
            id: 4,
            name: "Dr. Thomas Mwangi",
            specialty: "Couples Therapy",
            image: "assets/images/therapist4.jpg",
            availability: ["Wednesday", "Friday", "Saturday"]
        }
    ];
    
    // Calendar variables
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();
    
    // Open modal event
    if (openModalBtn) {
        openModalBtn.addEventListener('click', function() {
            if (modal) {
                modal.style.display = 'block';
                // Initialize the booking form
                loadTherapists();
                renderCalendar();
            }
        });
    }
    
    // Close modal events
    if (closeButtons) {
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (modal) modal.style.display = 'none';
                if (confirmationModal) confirmationModal.style.display = 'none';
            });
        });
    }
    
    if (closeConfirmationBtn) {
        closeConfirmationBtn.addEventListener('click', function() {
            if (confirmationModal) {
                confirmationModal.style.display = 'none';
            }
        });
    }
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (modal && event.target === modal) {
            modal.style.display = 'none';
        }
        if (confirmationModal && event.target === confirmationModal) {
            confirmationModal.style.display = 'none';
        }
    });
    
    // Load therapists into the grid
    function loadTherapists() {
        if (!therapistGrid) return;
        
        therapistGrid.innerHTML = '';
        
        therapists.forEach(therapist => {
            const therapistCard = document.createElement('div');
            therapistCard.className = 'therapist-card';
            therapistCard.setAttribute('data-id', therapist.id);
            
            const therapistImage = document.createElement('div');
            therapistImage.className = 'therapist-image';
            therapistImage.style.backgroundImage = `url(${therapist.image})`;
            
            const therapistInfo = document.createElement('div');
            therapistInfo.className = 'therapist-info';
            
            therapistInfo.innerHTML = `
                <h4>${therapist.name}</h4>
                <p>${therapist.specialty}</p>
                <p class="availability">Available: ${therapist.availability.join(', ')}</p>
            `;
            
            therapistCard.appendChild(therapistImage);
            therapistCard.appendChild(therapistInfo);
            
            // Select therapist event
            therapistCard.addEventListener('click', function() {
                document.querySelectorAll('.therapist-card').forEach(card => {
                    card.classList.remove('selected');
                });
                
                therapistCard.classList.add('selected');
                bookingState.therapist = therapist;
                if (summaryTherapist) summaryTherapist.textContent = therapist.name;
                updateBookingButton();
            });
            
            therapistGrid.appendChild(therapistCard);
        });
    }
    
    // Appointment type selection
    if (appointmentTypes) {
        appointmentTypes.addEventListener('click', function(e) {
            const typeOption = e.target.closest('.type-option');
            
            if (typeOption) {
                document.querySelectorAll('.type-option').forEach(option => {
                    option.classList.remove('selected');
                });
                
                typeOption.classList.add('selected');
                const type = typeOption.getAttribute('data-type');
                const price = parseInt(typeOption.getAttribute('data-price'));
                
                bookingState.appointmentType = type;
                bookingState.price = price;
                
                if (summaryType) summaryType.textContent = typeOption.querySelector('h4').textContent;
                if (summaryPrice) summaryPrice.textContent = `KSH ${price}`;
                
                updateBookingButton();
            }
        });
    }
    
    // Calendar navigation
    if (prevMonthBtn) {
        prevMonthBtn.addEventListener('click', function() {
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            renderCalendar();
        });
    }
    
    if (nextMonthBtn) {
        nextMonthBtn.addEventListener('click', function() {
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            renderCalendar();
        });
    }
    
    // Render calendar
    function renderCalendar() {
        if (!calendarGrid || !currentMonthDisplay) return;
        
        const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        currentMonthDisplay.textContent = `${monthNames[currentMonth]} ${currentYear}`;
        
        calendarGrid.innerHTML = '';
        
        // Add day header row
        const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
        dayNames.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'calendar-day-header';
            dayHeader.textContent = day;
            calendarGrid.appendChild(dayHeader);
        });
        
        // Get first day of month
        const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay();
        
        // Get last day of month
        const lastDay = new Date(currentYear, currentMonth + 1, 0).getDate();
        
        // Add empty cells for days before first day of month
        for (let i = 0; i < firstDayOfMonth; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'calendar-day empty';
            calendarGrid.appendChild(emptyDay);
        }
        
        // Add days of the month
        for (let day = 1; day <= lastDay; day++) {
            const date = new Date(currentYear, currentMonth, day);
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;
            
            // Disable past dates
            if (date < new Date().setHours(0, 0, 0, 0)) {
                dayElement.classList.add('disabled');
            } else {
                dayElement.addEventListener('click', function() {
                    document.querySelectorAll('.calendar-day').forEach(d => {
                        d.classList.remove('selected');
                    });
                    
                    dayElement.classList.add('selected');
                    bookingState.date = date;
                    if (summaryDate) summaryDate.textContent = date.toLocaleDateString('en-GB');
                    
                    // Generate time slots based on selected date
                    generateTimeSlots(date);
                    updateBookingButton();
                });
            }
            
            calendarGrid.appendChild(dayElement);
        }
    }
    
    // Generate time slots based on selected date
    function generateTimeSlots(date) {
        if (!timeSlots) return;
        
        timeSlots.innerHTML = '';
        
        // Example time slots (9am to 5pm, hourly)
        const hours = ["9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00"];
        
        hours.forEach(hour => {
            const timeSlot = document.createElement('div');
            timeSlot.className = 'time-slot';
            timeSlot.textContent = hour;
            
            // Randomly make some slots unavailable (for demo purposes)
            const isAvailable = Math.random() > 0.3;
            
            if (!isAvailable) {
                timeSlot.classList.add('unavailable');
                timeSlot.textContent += ' (Booked)';
            } else {
                timeSlot.addEventListener('click', function() {
                    document.querySelectorAll('.time-slot').forEach(slot => {
                        slot.classList.remove('selected');
                    });
                    
                    timeSlot.classList.add('selected');
                    bookingState.time = hour;
                    if (summaryTime) summaryTime.textContent = hour;
                    updateBookingButton();
                });
            }
            
            timeSlots.appendChild(timeSlot);
        });
    }
    
    // Update booking button state
    function updateBookingButton() {
        if (!bookingBtn) return;
        
        if (bookingState.therapist && bookingState.appointmentType && bookingState.date && bookingState.time) {
            bookingBtn.disabled = false;
        } else {
            bookingBtn.disabled = true;
        }
    }
    
    // Book appointment
    if (bookingBtn) {
        bookingBtn.addEventListener('click', function() {
            // Here you would typically send the booking data to your server
            // For this demo, we'll just show the confirmation modal
            
            const confirmationDetails = document.getElementById('confirmationDetails');
            if (confirmationDetails && summaryType && summaryDate && summaryTime) {
                confirmationDetails.innerHTML = `
                    <div class="confirmation-detail">
                        <span class="label">Therapist:</span>
                        <span class="value">${bookingState.therapist.name}</span>
                    </div>
                    <div class="confirmation-detail">
                        <span class="label">Session Type:</span>
                        <span class="value">${summaryType.textContent}</span>
                    </div>
                    <div class="confirmation-detail">
                        <span class="label">Date:</span>
                        <span class="value">${summaryDate.textContent}</span>
                    </div>
                    <div class="confirmation-detail">
                        <span class="label">Time:</span>
                        <span class="value">${summaryTime.textContent}</span>
                    </div>
                    <div class="confirmation-detail">
                        <span class="label">Total Amount:</span>
                        <span class="value">KSH ${bookingState.price}</span>
                    </div>
                `;
            }
            
            if (modal) modal.style.display = 'none';
            if (confirmationModal) confirmationModal.style.display = 'block';
            
            // Reset booking state
            resetBookingState();
        });
    }
    
    // Reset booking state
    function resetBookingState() {
        bookingState = {
            therapist: null,
            appointmentType: null,
            date: null,
            time: null,
            price: 0
        };
        
        // Reset UI selections
        document.querySelectorAll('.therapist-card, .type-option, .calendar-day, .time-slot').forEach(element => {
            element.classList.remove('selected');
        });
        
        // Reset summary
        if (summaryTherapist) summaryTherapist.textContent = 'Not selected';
        if (summaryType) summaryType.textContent = 'Not selected';
        if (summaryDate) summaryDate.textContent = 'Not selected';
        if (summaryTime) summaryTime.textContent = 'Not selected';
        if (summaryPrice) summaryPrice.textContent = 'KSH 0';
        
        // Disable book button
        if (bookingBtn) bookingBtn.disabled = true;
    }
});
class ChatManager {
    constructor() {
        this.ws = null;
        this.userId = null;
        this.currentChatId = null;
        this.typingTimeout = null;
        this.messageQueue = [];
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;

        this.initializeWebSocket();
        this.setupEventListeners();
    }

    initializeWebSocket() {
        this.ws = new WebSocket('ws://localhost:8080');

        this.ws.onopen = () => {
            console.log('Connected to chat server');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.authenticate();
            this.processMessageQueue();
        };

        this.ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleServerMessage(data);
        };

        this.ws.onclose = () => {
            console.log('Disconnected from chat server');
            this.isConnected = false;
            this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }

    setupEventListeners() {
        // Message input handling
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('input', () => {
                this.sendTypingStatus(true);
                clearTimeout(this.typingTimeout);
                this.typingTimeout = setTimeout(() => {
                    this.sendTypingStatus(false);
                }, 1000);
            });

            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }

        // Send button handling
        const sendButton = document.getElementById('send-message');
        if (sendButton) {
            sendButton.addEventListener('click', () => this.sendMessage());
        }

        // Chat selection handling
        document.addEventListener('click', (e) => {
            const chatItem = e.target.closest('.chat-item');
            if (chatItem) {
                const chatId = chatItem.dataset.chatId;
                this.switchChat(chatId);
            }
        });
    }

    authenticate() {
        if (this.userId) {
            this.sendToServer({
                type: 'auth',
                userId: this.userId
            });
        }
    }

    sendToServer(data) {
        if (this.isConnected) {
            this.ws.send(JSON.stringify(data));
        } else {
            this.messageQueue.push(data);
        }
    }

    processMessageQueue() {
        while (this.messageQueue.length > 0) {
            const data = this.messageQueue.shift();
            this.sendToServer(data);
        }
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.initializeWebSocket();
            }, 5000 * this.reconnectAttempts);
        }
    }

    sendMessage() {
        const messageInput = document.getElementById('message-input');
        const content = messageInput.value.trim();
        
        if (content && this.currentChatId) {
            this.sendToServer({
                type: 'message',
                chatId: this.currentChatId,
                senderId: this.userId,
                content: content,
                recipientId: this.getRecipientId()
            });

            messageInput.value = '';
            this.sendTypingStatus(false);
        }
    }

    sendTypingStatus(isTyping) {
        if (this.currentChatId) {
            this.sendToServer({
                type: 'typing',
                chatId: this.currentChatId,
                userId: this.userId,
                isTyping: isTyping,
                recipientId: this.getRecipientId()
            });
        }
    }

    handleServerMessage(data) {
        switch (data.type) {
            case 'message':
                this.displayMessage(data);
                break;
            case 'typing':
                this.updateTypingStatus(data);
                break;
            case 'read_receipt':
                this.updateReadReceipts(data);
                break;
            case 'error':
                console.error('Server error:', data.message);
                break;
        }
    }

    displayMessage(message) {
        const chatMessages = document.querySelector('.chat-messages');
        if (!chatMessages) return;

        const messageElement = document.createElement('div');
        messageElement.className = `message ${message.sender_id === this.userId ? 'sent' : 'received'}`;
        messageElement.dataset.messageId = message.id;

        messageElement.innerHTML = `
            <div class="message-content">
                <p>${this.escapeHtml(message.content)}</p>
                <span class="message-time">${this.formatTime(message.sent_time)}</span>
                ${message.sender_id === this.userId ? 
                    `<span class="message-status">${message.is_read ? '✓✓' : '✓'}</span>` : ''}
            </div>
        `;

        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Mark message as read if it's received
        if (message.sender_id !== this.userId) {
            this.markMessageAsRead(message.id);
        }
    }

    updateTypingStatus(data) {
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            if (data.isTyping) {
                typingIndicator.textContent = 'Typing...';
                typingIndicator.style.display = 'block';
            } else {
                typingIndicator.style.display = 'none';
            }
        }
    }

    updateReadReceipts(data) {
        data.messageIds.forEach(messageId => {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                const statusElement = messageElement.querySelector('.message-status');
                if (statusElement) {
                    statusElement.textContent = '✓✓';
                }
            }
        });
    }

    markMessageAsRead(messageId) {
        this.sendToServer({
            type: 'read',
            chatId: this.currentChatId,
            messageIds: [messageId],
            userId: this.userId
        });
    }

    switchChat(chatId) {
        this.currentChatId = chatId;
        this.loadChatHistory(chatId);
        this.updateChatUI(chatId);
    }

    async loadChatHistory(chatId) {
        try {
            const response = await fetch(`/api/messages.php?chat_id=${chatId}`);
            const messages = await response.json();
            
            const chatMessages = document.querySelector('.chat-messages');
            if (chatMessages) {
                chatMessages.innerHTML = '';
                messages.forEach(message => this.displayMessage(message));
            }
        } catch (error) {
            console.error('Error loading chat history:', error);
        }
    }

    updateChatUI(chatId) {
        // Update active chat in sidebar
        document.querySelectorAll('.chat-item').forEach(item => {
            item.classList.toggle('active', item.dataset.chatId === chatId);
        });

        // Update chat header
        const chatHeader = document.querySelector('.chat-header');
        if (chatHeader) {
            const chatName = document.querySelector(`.chat-item[data-chat-id="${chatId}"] .chat-name`).textContent;
            chatHeader.querySelector('.chat-title').textContent = chatName;
        }
    }

    getRecipientId() {
        const activeChat = document.querySelector('.chat-item.active');
        return activeChat ? activeChat.dataset.recipientId : null;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
}

// Initialize chat when document is ready
document.addEventListener('DOMContentLoaded', () => {
    const chatManager = new ChatManager();
    window.chatManager = chatManager; // Make it accessible globally if needed
});
// WebSocket connection for real-time notifications
let notificationSocket;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;

function initNotifications() {
    connectWebSocket();
    loadNotifications();
    setupNotificationHandlers();
}

function connectWebSocket() {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${wsProtocol}//${window.location.hostname}:8080`;
    
    notificationSocket = new WebSocket(wsUrl);
    
    notificationSocket.onopen = () => {
        console.log('Connected to notification server');
        reconnectAttempts = 0;
        
        // Send authentication data
        const authData = {
            userId: window.userId,
            userRole: window.userRole,
            sessionId: window.sessionId
        };
        notificationSocket.send(JSON.stringify(authData));
    };
    
    notificationSocket.onmessage = (event) => {
        const notification = JSON.parse(event.data);
        handleNewNotification(notification);
    };
    
    notificationSocket.onclose = () => {
        console.log('Disconnected from notification server');
        if (reconnectAttempts < maxReconnectAttempts) {
            setTimeout(() => {
                reconnectAttempts++;
                connectWebSocket();
            }, 3000);
        }
    };
    
    notificationSocket.onerror = (error) => {
        console.error('WebSocket error:', error);
    };
}

function loadNotifications() {
    fetch('/api/notifications.php')
        .then(response => response.json())
        .then(data => {
            updateNotificationBadge(data.unreadCount);
            updateNotificationList(data.notifications);
        })
        .catch(error => console.error('Error loading notifications:', error));
}

function handleNewNotification(notification) {
    // Update notification badge
    const badge = document.getElementById('notification-badge');
    const currentCount = parseInt(badge.textContent) || 0;
    badge.textContent = currentCount + 1;
    badge.style.display = 'block';
    
    // Add notification to list
    const notificationList = document.getElementById('notification-list');
    const notificationItem = createNotificationElement(notification);
    notificationList.insertBefore(notificationItem, notificationList.firstChild);
    
    // Show notification toast
    showNotificationToast(notification);
}

function createNotificationElement(notification) {
    const li = document.createElement('li');
    li.className = 'notification-item' + (notification.is_read ? '' : ' unread');
    li.dataset.notificationId = notification.id;
    
    li.innerHTML = `
        <div class="notification-content">
            <p class="notification-message">${notification.message}</p>
            <span class="notification-time">${formatTimestamp(notification.created_at)}</span>
        </div>
        <button class="mark-read-btn" onclick="markAsRead(${notification.id})">
            <i class="fas fa-check"></i>
        </button>
    `;
    
    return li;
}

function showNotificationToast(notification) {
    const toast = document.createElement('div');
    toast.className = 'notification-toast';
    toast.innerHTML = `
        <div class="notification-toast-content">
            <i class="fas fa-bell"></i>
            <p>${notification.message}</p>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Trigger animation
    setTimeout(() => toast.classList.add('show'), 100);
    
    // Remove toast after 5 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, 5000);
}

function markAsRead(notificationId) {
    fetch('/api/notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'mark_read',
            notification_id: notificationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const item = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (item) {
                item.classList.remove('unread');
            }
            updateNotificationBadge(data.unreadCount);
        }
    })
    .catch(error => console.error('Error marking notification as read:', error));
}

function updateNotificationBadge(count) {
    const badge = document.getElementById('notification-badge');
    if (count > 0) {
        badge.textContent = count;
        badge.style.display = 'block';
    } else {
        badge.style.display = 'none';
    }
}

function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // less than 1 minute
        return 'Just now';
    } else if (diff < 3600000) { // less than 1 hour
        const minutes = Math.floor(diff / 60000);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diff < 86400000) { // less than 1 day
        const hours = Math.floor(diff / 3600000);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
        return date.toLocaleDateString();
    }
}

// Initialize notifications when document is ready
document.addEventListener('DOMContentLoaded', initNotifications);
class ResourceManager {
    constructor() {
        this.resources = [];
        this.currentFilter = 'all';
        this.searchTerm = '';
        this.modal = document.getElementById('resourceModal');
        this.searchInput = document.getElementById('resourceSearch');
        this.filterButtons = document.querySelectorAll('.filter-btn');
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadResources();
    }
    
    setupEventListeners() {
        // Search functionality
        this.searchInput.addEventListener('input', (e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.filterResources();
        });
        
        // Filter buttons
        this.filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.filterButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                this.currentFilter = button.dataset.category;
                this.filterResources();
            });
        });
        
        // Modal close button
        const closeModal = this.modal.querySelector('.close-modal');
        closeModal.addEventListener('click', () => this.closeModal());
        
        // Close modal when clicking outside
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });
    }
    
    async loadResources() {
        try {
            const response = await fetch('/api/resources');
            this.resources = await response.json();
            this.displayResources(this.resources);
        } catch (error) {
            console.error('Error loading resources:', error);
            this.showError('Failed to load resources. Please try again later.');
        }
    }
    
    filterResources() {
        let filteredResources = [...this.resources];
        
        // Apply category filter
        if (this.currentFilter !== 'all') {
            filteredResources = filteredResources.filter(resource => 
                resource.category === this.currentFilter
            );
        }
        
        // Apply search filter
        if (this.searchTerm) {
            filteredResources = filteredResources.filter(resource =>
                resource.title.toLowerCase().includes(this.searchTerm) ||
                resource.description.toLowerCase().includes(this.searchTerm)
            );
        }
        
        this.displayResources(filteredResources);
    }
    
    displayResources(resources) {
        const resourceLists = document.querySelectorAll('.resource-list');
        
        // Clear existing resources
        resourceLists.forEach(list => list.innerHTML = '');
        
        // Group resources by category
        const resourcesByCategory = resources.reduce((acc, resource) => {
            if (!acc[resource.category]) {
                acc[resource.category] = [];
            }
            acc[resource.category].push(resource);
            return acc;
        }, {});
        
        // Display resources in their respective categories
        Object.entries(resourcesByCategory).forEach(([category, categoryResources]) => {
            const categoryElement = document.querySelector(`.resource-category[data-category="${category}"]`);
            if (categoryElement) {
                const resourceList = categoryElement.querySelector('.resource-list');
                categoryResources.forEach(resource => {
                    resourceList.appendChild(this.createResourceCard(resource));
                });
            }
        });
    }
    
    createResourceCard(resource) {
        const card = document.createElement('div');
        card.className = 'resource-card';
        card.innerHTML = `
            <div class="resource-icon">
                <i class="${resource.icon}"></i>
            </div>
            <h3>${this.escapeHtml(resource.title)}</h3>
            <p>${this.escapeHtml(resource.description)}</p>
            <div class="resource-meta">
                <span><i class="fas fa-clock"></i> ${resource.duration}</span>
                <span><i class="fas fa-calendar"></i> ${this.formatDate(resource.date)}</span>
            </div>
        `;
        
        card.addEventListener('click', () => this.openResourceModal(resource));
        return card;
    }
    
    openResourceModal(resource) {
        const modal = this.modal;
        const header = modal.querySelector('.modal-header h2');
        const date = modal.querySelector('.date');
        const duration = modal.querySelector('.duration');
        const category = modal.querySelector('.category');
        const image = modal.querySelector('.resource-image');
        const content = modal.querySelector('.resource-content');
        const downloadBtn = modal.querySelector('.resource-download button');
        
        header.textContent = resource.title;
        date.textContent = this.formatDate(resource.date);
        duration.textContent = resource.duration;
        category.textContent = resource.category;
        
        if (resource.image) {
            image.src = resource.image;
            image.alt = resource.title;
            image.style.display = 'block';
        } else {
            image.style.display = 'none';
        }
        
        content.innerHTML = resource.content;
        
        if (resource.downloadUrl) {
            downloadBtn.style.display = 'block';
            downloadBtn.onclick = () => this.downloadResource(resource.downloadUrl);
        } else {
            downloadBtn.style.display = 'none';
        }
        
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
    
    closeModal() {
        this.modal.style.display = 'none';
        document.body.style.overflow = '';
    }
    
    async downloadResource(url) {
        try {
            const response = await fetch(url);
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = url.split('/').pop();
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(downloadUrl);
        } catch (error) {
            console.error('Error downloading resource:', error);
            this.showError('Failed to download resource. Please try again later.');
        }
    }
    
    showError(message) {
        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        // Remove notification after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
    
    escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }
}

// Initialize ResourceManager when the document is ready
document.addEventListener('DOMContentLoaded', () => {
    window.resourceManager = new ResourceManager();
}); 
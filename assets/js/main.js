// Add version parameter to fetch requests
function addVersionToUrl(url) {
    return `${url}${url.includes('?') ? '&' : '?'}v=${new Date().getTime()}`;
}

// Mood Tracking Chart
function initMoodChart() {
    const ctx = document.getElementById('moodChart').getContext('2d');
    const moodChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [], // Will be populated with dates
            datasets: [{
                label: 'Mood Score',
                data: [], // Will be populated with mood scores
                borderColor: '#7C65B3',
                tension: 0.4,
                fill: true,
                backgroundColor: 'rgba(124, 101, 179, 0.1)'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 10,
                    grid: {
                        display: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Add version parameter to any fetch requests
    fetch(addVersionToUrl('/api/mood-data'))
        .then(response => response.json())
        .then(data => {
            // Update chart with data
        });

    return moodChart;
}

// Journal Entry Character Counter
function initJournalCounter() {
    const journalText = document.getElementById('journalEntry');
    const counter = document.getElementById('charCounter');
    
    if (journalText && counter) {
        journalText.addEventListener('input', function() {
            counter.textContent = `${this.value.length}/2000`;
        });
    }
}

// Mood Selection Animation
function initMoodSelection() {
    const moodButtons = document.querySelectorAll('.mood-btn');
    
    moodButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            moodButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            document.getElementById('moodScore').value = this.dataset.score;
        });
    });
}

// Force reload on theme change
function reloadPage() {
    window.location.reload(true);
}

// Initialize all components
document.addEventListener('DOMContentLoaded', function() {
    initJournalCounter();
    initMoodSelection();
    
    if (document.getElementById('moodChart')) {
        const chart = initMoodChart();
    }
}); 

// Journal Manager - Handles journal entries, notifications, and resources
class JournalManager {
    constructor() {
        this.currentMood = null;
        this.notificationSocket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reminderInterval = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.connectNotificationSocket();
        this.loadJournalEntries();
        this.setupJournalReminders();
        this.loadResources();
    }
    
    setupEventListeners() {
        // Mood selection
        const moodButtons = document.querySelectorAll('.mood-btn');
        moodButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.selectMood(button);
            });
        });
        
        // New journal entry
        const newEntryBtn = document.getElementById('new-entry-btn');
        if (newEntryBtn) {
            newEntryBtn.addEventListener('click', () => {
                this.showNewEntryModal();
            });
        }
        
        // Journal entry form submission
        const journalForm = document.getElementById('journal-form');
        if (journalForm) {
            journalForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveJournalEntry();
            });
        }
        
        // Resource filters
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.filterResources(button);
            });
        });
        
        // Resource actions
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('view-resource-btn')) {
                const resourceId = e.target.dataset.resourceId;
                this.viewResource(resourceId);
            }
        });
        
        // Close modal
        const closeButtons = document.querySelectorAll('.close');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.closeModal();
            });
        });
    }
    
    connectNotificationSocket() {
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${wsProtocol}//${window.location.hostname}:8080`;
        
        this.notificationSocket = new WebSocket(wsUrl);
        
        this.notificationSocket.onopen = () => {
            console.log('Connected to notification server');
            this.reconnectAttempts = 0;
            
            // Send authentication data
            const authData = {
                type: 'auth',
                userId: window.userId,
                userRole: window.userRole,
                sessionId: window.sessionId
            };
            this.notificationSocket.send(JSON.stringify(authData));
        };
        
        this.notificationSocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleNotification(data);
        };
        
        this.notificationSocket.onclose = () => {
            console.log('Disconnected from notification server');
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                setTimeout(() => {
                    this.reconnectAttempts++;
                    this.connectNotificationSocket();
                }, 3000);
            }
        };
        
        this.notificationSocket.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }
    
    handleNotification(notification) {
        if (notification.type === 'journal_reminder') {
            this.showJournalReminder(notification);
        } else if (notification.type === 'journal_insight') {
            this.showJournalInsight(notification);
        } else if (notification.type === 'resource_recommendation') {
            this.showResourceRecommendation(notification);
        }
    }
    
    showJournalReminder(notification) {
        const toast = document.createElement('div');
        toast.className = 'notification-toast journal-reminder';
        toast.innerHTML = `
            <div class="notification-toast-content">
                <i class="fas fa-book"></i>
                <div class="notification-text">
                    <h4>${notification.title}</h4>
                    <p>${notification.message}</p>
                </div>
                <button class="write-now-btn" onclick="journalManager.showNewEntryModal()">Write Now</button>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Remove toast after 10 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 10000);
    }
    
    showJournalInsight(notification) {
        const insightModal = document.createElement('div');
        insightModal.className = 'modal insight-modal';
        insightModal.innerHTML = `
            <div class="modal-content">
                <button class="close">&times;</button>
                <div class="insight-header">
                    <i class="fas fa-lightbulb"></i>
                    <h2>Journal Insight</h2>
                </div>
                <div class="insight-content">
                    <h3>${notification.title}</h3>
                    <p>${notification.message}</p>
                    <div class="insight-data">
                        ${notification.data ? this.formatInsightData(notification.data) : ''}
                    </div>
                </div>
                <div class="insight-actions">
                    <button class="btn-secondary" onclick="journalManager.closeModal()">Close</button>
                    <button class="btn-primary" onclick="journalManager.showNewEntryModal()">Write About This</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(insightModal);
        
        // Add event listener to close button
        const closeBtn = insightModal.querySelector('.close');
        closeBtn.addEventListener('click', () => {
            this.closeModal();
        });
    }
    
    showResourceRecommendation(notification) {
        const resourceModal = document.createElement('div');
        resourceModal.className = 'modal resource-modal';
        resourceModal.innerHTML = `
            <div class="modal-content">
                <button class="close">&times;</button>
                <div class="resource-recommendation">
                    <h2>Recommended Resource</h2>
                    <div class="resource-card">
                        <div class="resource-image">
                            <img src="${notification.resource.image_url || 'assets/images/default-resource.jpg'}" alt="${notification.resource.title}">
                        </div>
                        <div class="resource-content">
                            <span class="resource-type">${notification.resource.category_name}</span>
                            <h3 class="resource-title">${notification.resource.title}</h3>
                            <p class="resource-description">${notification.resource.description}</p>
                            <div class="resource-actions">
                                <button class="action-btn primary-action view-resource-btn" data-resource-id="${notification.resource.id}">View Resource</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(resourceModal);
        
        // Add event listener to close button
        const closeBtn = resourceModal.querySelector('.close');
        closeBtn.addEventListener('click', () => {
            this.closeModal();
        });
    }
    
    formatInsightData(data) {
        if (!data) return '';
        
        let html = '<div class="insight-stats">';
        
        if (data.mood_trend) {
            html += `
                <div class="insight-stat">
                    <h4>Mood Trend</h4>
                    <div class="mood-trend-chart">
                        ${this.generateMoodTrendChart(data.mood_trend)}
                    </div>
                </div>
            `;
        }
        
        if (data.common_themes) {
            html += `
                <div class="insight-stat">
                    <h4>Common Themes</h4>
                    <ul class="theme-list">
                        ${data.common_themes.map(theme => `<li>${theme}</li>`).join('')}
                    </ul>
                </div>
            `;
        }
        
        if (data.writing_frequency) {
            html += `
                <div class="insight-stat">
                    <h4>Writing Frequency</h4>
                    <p>${data.writing_frequency}</p>
                </div>
            `;
        }
        
        html += '</div>';
        return html;
    }
    
    generateMoodTrendChart(moodTrend) {
        // Simple visualization of mood trend
        let html = '<div class="mood-chart">';
        
        moodTrend.forEach((mood, index) => {
            const height = (mood.value / 5) * 100; // Assuming mood values are 1-5
            const color = this.getMoodColor(mood.name);
            
            html += `
                <div class="mood-bar" style="height: ${height}%; background-color: ${color};" 
                     title="${mood.name}: ${mood.value}/5"></div>
            `;
        });
        
        html += '</div>';
        return html;
    }
    
    getMoodColor(moodName) {
        const moodColors = {
            'Happy': '#65B3A0',
            'Calm': '#B3A0E8',
            'Anxious': '#E88A72',
            'Sad': '#72A0E8',
            'Angry': '#E87272',
            'Excited': '#E8B372',
            'Tired': '#A0A0A0',
            'Energetic': '#72E872'
        };
        
        return moodColors[moodName] || '#B3A0E8';
    }
    
    selectMood(button) {
        // Remove active class from all mood buttons
        document.querySelectorAll('.mood-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Add active class to selected button
        button.classList.add('active');
        
        // Store selected mood
        this.currentMood = button.dataset.mood;
        
        // Update mood input in form
        const moodInput = document.getElementById('mood-input');
        if (moodInput) {
            moodInput.value = this.currentMood;
        }
    }
    
    showNewEntryModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'journal-modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <button class="close">&times;</button>
                <h2>New Journal Entry</h2>
                <form id="journal-form">
                    <div class="form-group">
                        <label for="mood-input">How are you feeling today?</label>
                        <div class="mood-options">
                            <button type="button" class="mood-btn" data-mood="Happy">
                                <i class="fas fa-smile"></i> Happy
                            </button>
                            <button type="button" class="mood-btn" data-mood="Calm">
                                <i class="fas fa-meh"></i> Calm
                            </button>
                            <button type="button" class="mood-btn" data-mood="Anxious">
                                <i class="fas fa-frown"></i> Anxious
                            </button>
                            <button type="button" class="mood-btn" data-mood="Sad">
                                <i class="fas fa-sad-tear"></i> Sad
                            </button>
                            <button type="button" class="mood-btn" data-mood="Angry">
                                <i class="fas fa-angry"></i> Angry
                            </button>
                            <button type="button" class="mood-btn" data-mood="Excited">
                                <i class="fas fa-laugh"></i> Excited
                            </button>
                            <button type="button" class="mood-btn" data-mood="Tired">
                                <i class="fas fa-tired"></i> Tired
                            </button>
                            <button type="button" class="mood-btn" data-mood="Energetic">
                                <i class="fas fa-bolt"></i> Energetic
                            </button>
                        </div>
                        <input type="hidden" id="mood-input" name="mood" value="">
                    </div>
                    <div class="form-group">
                        <label for="journal-content">What's on your mind?</label>
                        <textarea id="journal-content" name="content" rows="10" placeholder="Write your thoughts here..."></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="journalManager.closeModal()">Cancel</button>
                        <button type="submit" class="btn-primary">Save Entry</button>
                    </div>
                </form>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add event listener to close button
        const closeBtn = modal.querySelector('.close');
        closeBtn.addEventListener('click', () => {
            this.closeModal();
        });
    }
    
    closeModal() {
        const modal = document.querySelector('.modal');
        if (modal) {
            modal.remove();
        }
    }
    
    saveJournalEntry() {
        const content = document.getElementById('journal-content').value;
        const mood = document.getElementById('mood-input').value;
        
        if (!content.trim()) {
            alert('Please write something in your journal entry.');
            return;
        }
        
        fetch('/api/journal.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                content: content,
                mood: mood
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.closeModal();
                this.loadJournalEntries();
                this.showSuccessMessage('Journal entry saved successfully!');
            } else {
                this.showErrorMessage(data.message || 'Failed to save journal entry.');
            }
        })
        .catch(error => {
            console.error('Error saving journal entry:', error);
            this.showErrorMessage('An error occurred while saving your journal entry.');
        });
    }
    
    loadJournalEntries() {
        fetch('/api/journal.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.displayJournalEntries(data.entries);
                } else {
                    this.showErrorMessage(data.message || 'Failed to load journal entries.');
                }
            })
            .catch(error => {
                console.error('Error loading journal entries:', error);
                this.showErrorMessage('An error occurred while loading your journal entries.');
            });
    }
    
    displayJournalEntries(entries) {
        const journalList = document.querySelector('.journal-entries');
        if (!journalList) return;
        
        journalList.innerHTML = '';
        
        if (entries.length === 0) {
            journalList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-book"></i>
                    <h3>No Journal Entries Yet</h3>
                    <p>Start your journaling journey by writing your first entry.</p>
                    <button class="btn-primary" onclick="journalManager.showNewEntryModal()">Write Your First Entry</button>
                </div>
            `;
            return;
        }
        
        entries.forEach(entry => {
            const entryElement = document.createElement('div');
            entryElement.className = 'journal-card';
            entryElement.innerHTML = `
                <div class="entry-header">
                    <div class="entry-date">
                        <i class="far fa-calendar-alt"></i>
                        ${this.formatDate(entry.created_at)}
                    </div>
                    ${entry.mood ? `
                        <div class="entry-mood" style="background-color: ${this.getMoodColor(entry.mood)}">
                            <i class="fas fa-smile"></i> ${entry.mood}
                        </div>
                    ` : ''}
                </div>
                <div class="entry-content">${this.formatContent(entry.content)}</div>
                <div class="entry-actions">
                    <button class="btn-icon" onclick="journalManager.editEntry(${entry.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" onclick="journalManager.deleteEntry(${entry.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            
            journalList.appendChild(entryElement);
        });
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    formatContent(content) {
        // Convert URLs to links
        content = content.replace(
            /(https?:\/\/[^\s]+)/g, 
            '<a href="$1" target="_blank">$1</a>'
        );
        
        // Convert line breaks to <br>
        content = content.replace(/\n/g, '<br>');
        
        return content;
    }
    
    editEntry(entryId) {
        fetch(`/api/journal.php?id=${entryId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.showEditEntryModal(data.entry);
                } else {
                    this.showErrorMessage(data.message || 'Failed to load journal entry.');
                }
            })
            .catch(error => {
                console.error('Error loading journal entry:', error);
                this.showErrorMessage('An error occurred while loading your journal entry.');
            });
    }
    
    showEditEntryModal(entry) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'journal-modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <button class="close">&times;</button>
                <h2>Edit Journal Entry</h2>
                <form id="journal-form">
                    <input type="hidden" id="entry-id" value="${entry.id}">
                    <div class="form-group">
                        <label for="mood-input">How were you feeling?</label>
                        <div class="mood-options">
                            <button type="button" class="mood-btn ${entry.mood === 'Happy' ? 'active' : ''}" data-mood="Happy">
                                <i class="fas fa-smile"></i> Happy
                            </button>
                            <button type="button" class="mood-btn ${entry.mood === 'Calm' ? 'active' : ''}" data-mood="Calm">
                                <i class="fas fa-meh"></i> Calm
                            </button>
                            <button type="button" class="mood-btn ${entry.mood === 'Anxious' ? 'active' : ''}" data-mood="Anxious">
                                <i class="fas fa-frown"></i> Anxious
                            </button>
                            <button type="button" class="mood-btn ${entry.mood === 'Sad' ? 'active' : ''}" data-mood="Sad">
                                <i class="fas fa-sad-tear"></i> Sad
                            </button>
                            <button type="button" class="mood-btn ${entry.mood === 'Angry' ? 'active' : ''}" data-mood="Angry">
                                <i class="fas fa-angry"></i> Angry
                            </button>
                            <button type="button" class="mood-btn ${entry.mood === 'Excited' ? 'active' : ''}" data-mood="Excited">
                                <i class="fas fa-laugh"></i> Excited
                            </button>
                            <button type="button" class="mood-btn ${entry.mood === 'Tired' ? 'active' : ''}" data-mood="Tired">
                                <i class="fas fa-tired"></i> Tired
                            </button>
                            <button type="button" class="mood-btn ${entry.mood === 'Energetic' ? 'active' : ''}" data-mood="Energetic">
                                <i class="fas fa-bolt"></i> Energetic
                            </button>
                        </div>
                        <input type="hidden" id="mood-input" name="mood" value="${entry.mood || ''}">
                    </div>
                    <div class="form-group">
                        <label for="journal-content">What's on your mind?</label>
                        <textarea id="journal-content" name="content" rows="10">${entry.content}</textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="journalManager.closeModal()">Cancel</button>
                        <button type="submit" class="btn-primary">Update Entry</button>
                    </div>
                </form>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add event listener to close button
        const closeBtn = modal.querySelector('.close');
        closeBtn.addEventListener('click', () => {
            this.closeModal();
        });
        
        // Set current mood
        this.currentMood = entry.mood;
    }
    
    deleteEntry(entryId) {
        if (confirm('Are you sure you want to delete this journal entry? This action cannot be undone.')) {
            fetch(`/api/journal.php?id=${entryId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.loadJournalEntries();
                    this.showSuccessMessage('Journal entry deleted successfully!');
                } else {
                    this.showErrorMessage(data.message || 'Failed to delete journal entry.');
                }
            })
            .catch(error => {
                console.error('Error deleting journal entry:', error);
                this.showErrorMessage('An error occurred while deleting your journal entry.');
            });
        }
    }
    
    setupJournalReminders() {
        // Check if user has set up journal reminders
        fetch('/api/user_settings.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.settings.journal_reminder_enabled) {
                    // Set up reminder interval
                    this.reminderInterval = setInterval(() => {
                        this.checkJournalReminder();
                    }, 60000); // Check every minute
                }
            })
            .catch(error => {
                console.error('Error loading user settings:', error);
            });
    }
    
    checkJournalReminder() {
        const now = new Date();
        const lastEntry = localStorage.getItem('lastJournalEntry');
        
        if (!lastEntry) {
            // No entries yet, show reminder after 24 hours
            this.showJournalReminder({
                title: 'Start Your Journaling Journey',
                message: 'Take a moment to reflect on your day and write your first journal entry.'
            });
            return;
        }
        
        const lastEntryDate = new Date(lastEntry);
        const hoursSinceLastEntry = (now - lastEntryDate) / (1000 * 60 * 60);
        
        // Show reminder if it's been more than 24 hours since last entry
        if (hoursSinceLastEntry > 24) {
            this.showJournalReminder({
                title: 'Time to Journal',
                message: `It's been ${Math.floor(hoursSinceLastEntry)} hours since your last journal entry. Take a moment to reflect on your day.`
            });
        }
    }
    
    loadResources() {
        fetch('/api/resources.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.displayResources(data.resources);
                } else {
                    this.showErrorMessage(data.message || 'Failed to load resources.');
                }
            })
            .catch(error => {
                console.error('Error loading resources:', error);
                this.showErrorMessage('An error occurred while loading resources.');
            });
    }
    
    displayResources(resources) {
        const resourcesGrid = document.querySelector('.resources-grid');
        if (!resourcesGrid) return;
        
        resourcesGrid.innerHTML = '';
        
        if (resources.length === 0) {
            resourcesGrid.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-book-open"></i>
                    <h3>No Resources Available</h3>
                    <p>Check back later for new mental health and journaling resources.</p>
                </div>
            `;
            return;
        }
        
        resources.forEach(resource => {
            const resourceElement = document.createElement('div');
            resourceElement.className = 'resource-card';
            resourceElement.innerHTML = `
                <div class="resource-image">
                    <img src="${resource.image_url || 'assets/images/default-resource.jpg'}" alt="${resource.title}">
                </div>
                <div class="resource-content">
                    <span class="resource-type">${resource.category_name}</span>
                    <h3 class="resource-title">${resource.title}</h3>
                    <p class="resource-description">${resource.description}</p>
                    <div class="resource-tags">
                        ${resource.tags ? resource.tags.map(tag => `<span class="resource-tag">${tag}</span>`).join('') : ''}
                    </div>
                    <div class="resource-actions">
                        <button class="action-btn primary-action view-resource-btn" data-resource-id="${resource.id}">View Resource</button>
                    </div>
                </div>
            `;
            
            resourcesGrid.appendChild(resourceElement);
        });
    }
    
    filterResources(button) {
        // Remove active class from all filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Add active class to selected button
        button.classList.add('active');
        
        const category = button.dataset.category;
        
        fetch(`/api/resources.php?category=${category}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.displayResources(data.resources);
                } else {
                    this.showErrorMessage(data.message || 'Failed to filter resources.');
                }
            })
            .catch(error => {
                console.error('Error filtering resources:', error);
                this.showErrorMessage('An error occurred while filtering resources.');
            });
    }
    
    viewResource(resourceId) {
        fetch(`/api/resources.php?id=${resourceId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.showResourceModal(data.resource);
                } else {
                    this.showErrorMessage(data.message || 'Failed to load resource.');
                }
            })
            .catch(error => {
                console.error('Error loading resource:', error);
                this.showErrorMessage('An error occurred while loading the resource.');
            });
    }
    
    showResourceModal(resource) {
        const modal = document.createElement('div');
        modal.className = 'modal resource-modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <button class="close">&times;</button>
                <div class="resource-detail">
                    <div class="resource-header">
                        <span class="resource-type">${resource.category_name}</span>
                        <h2>${resource.title}</h2>
                    </div>
                    <div class="resource-body">
                        <div class="resource-image">
                            <img src="${resource.image_url || 'assets/images/default-resource.jpg'}" alt="${resource.title}">
                        </div>
                        <div class="resource-description">
                            ${resource.content}
                        </div>
                        ${resource.file_url ? `
                            <div class="resource-download">
                                <a href="${resource.file_url}" class="btn-primary" download>
                                    <i class="fas fa-download"></i> Download Resource
                                </a>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add event listener to close button
        const closeBtn = modal.querySelector('.close');
        closeBtn.addEventListener('click', () => {
            this.closeModal();
        });
    }
    
    showSuccessMessage(message) {
        const toast = document.createElement('div');
        toast.className = 'toast success-toast';
        toast.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(toast);
        
        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Remove toast after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
    
    showErrorMessage(message) {
        const toast = document.createElement('div');
        toast.className = 'toast error-toast';
        toast.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(toast);
        
        // Trigger animation
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Remove toast after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
}

// Initialize journal manager when document is ready
document.addEventListener('DOMContentLoaded', () => {
    window.journalManager = new JournalManager();
}); 
:root[data-theme="light"] {
    --settings-bg: #f5f7fa;
    --section-bg: #ffffff;
    --section-border: #e1e1e1;
    --input-bg: #ffffff;
    --input-border: #e1e1e1;
    --input-focus: #7C65B3;
    --toggle-bg: #e1e1e1;
    --toggle-active: #7C65B3;
    --section-shadow: rgba(0, 0, 0, 0.1);
}

:root[data-theme="dark"] {
    --settings-bg: #1a1a2e;
    --section-bg: #252541;
    --section-border: #333355;
    --input-bg: #1E1E32;
    --input-border: #333355;
    --input-focus: #9D84D2;
    --toggle-bg: #333355;
    --toggle-active: #9D84D2;
    --section-shadow: rgba(0, 0, 0, 0.3);
}

.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.settings-container h1 {
    font-size: 2.5rem;
    color: var(--text-color);
    margin-bottom: 2rem;
    font-weight: 700;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.settings-section {
    background: var(--section-bg);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid var(--section-border);
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px var(--section-shadow);
}

.settings-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px var(--section-shadow);
}

.settings-section h2 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.settings-section h2 i {
    color: var(--primary-color);
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.form-group label {
    color: var(--text-color);
    font-weight: 500;
    flex: 1;
}

select {
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    color: var(--text-color);
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 200px;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1em;
}

select:focus {
    border-color: var(--input-focus);
    outline: none;
    box-shadow: 0 0 0 3px rgba(124, 101, 179, 0.1);
}

.toggle-switch {
    position: relative;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--toggle-bg);
    transition: .4s;
    border-radius: 30px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: var(--toggle-active);
}

.toggle-switch input:checked + label:before {
    transform: translateX(30px);
}

@media (max-width: 768px) {
    .settings-container {
        padding: 1rem;
    }

    .settings-container h1 {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .settings-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .form-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    select {
        width: 100%;
    }

    .toggle-switch {
        align-self: flex-start;
    }
}
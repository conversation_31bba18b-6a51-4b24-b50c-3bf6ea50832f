.therapist-select-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: var(--bg-color);
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.5rem;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-modal:hover {
    background-color: var(--hover-color);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 80px);
}

.search-filters {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.search-input,
.specialization-filter {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 1rem;
}

.therapists-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.therapist-card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 15px;
    align-items: start;
    transition: transform 0.2s, box-shadow 0.2s;
}

.therapist-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
}

.therapist-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.therapist-info h4 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
}

.specialization {
    color: var(--primary-color);
    font-weight: 500;
    margin: 0;
}

.experience, .languages {
    color: var(--text-color-light);
    margin: 0;
}

.bio {
    margin: 10px 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.qualification {
    background-color: var(--bg-color-light);
    padding: 10px;
    border-radius: 6px;
    margin-top: 10px;
}

.qualification p {
    margin: 5px 0;
    font-size: 0.9rem;
}

.view-profile-btn, .start-chat-btn {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.view-profile-btn {
    background-color: var(--secondary-color);
    color: white;
    margin-right: 10px;
}

.start-chat-btn {
    background-color: var(--primary-color);
    color: white;
}

.view-profile-btn:hover, .start-chat-btn:hover {
    opacity: 0.9;
}

.therapist-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.therapist-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.therapist-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.therapist-info h4 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.2rem;
}

.specialization {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

.experience {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

.start-chat-btn {
    grid-column: 1 / -1;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.start-chat-btn:hover {
    background-color: var(--primary-hover);
}

.new-chat-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
    transition: background-color 0.2s;
}

.new-chat-btn:hover {
    background-color: var(--primary-hover);
}

@media (max-width: 768px) {
    .search-filters {
        grid-template-columns: 1fr;
    }

    .therapists-list {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }
}
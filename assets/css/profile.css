:root[data-theme="light"] {
    --profile-card-bg: #ffffff;
    --profile-border: #e1e1e1;
    --stats-bg: #f8f8f8;
    --activity-item-hover: #f5f5f5;
    --stat-icon-bg: #f0f0f0;
    --stat-icon-color: #555555;
    --transition-speed: 0.2s;
}

:root[data-theme="dark"] {
    --profile-card-bg: #252541;
    --profile-border: #333355;
    --stats-bg: #1E1E32;
    --activity-item-hover: #2A2A4A;
    --stat-icon-bg: #2A2A4A;
    --stat-icon-color: #cccccc;
    --transition-speed: 0.2s;
}

.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.profile-header {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 3rem;
    padding: 2rem;
    background: var(--profile-card-bg);
    border-radius: 20px;
    box-shadow: 0 4px 6px var(--shadow-color);
    transition: all var(--transition-speed) ease;
}

.profile-image-container {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto;
}

.profile-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
    box-shadow: 0 2px 4px var(--shadow-color);
}

.edit-image-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.profile-email {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.profile-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
}

.profile-bio {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.profile-actions {
    display: flex;
    gap: 1rem;
}

.edit-profile-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-btn {
    background: var(--primary-color);
    color: white;
    border: none;
}

.secondary-btn {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--stats-bg);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    transition: all var(--transition-speed) ease;
}

.stat-card i {
    font-size: 2rem;
    background: var(--stat-icon-bg);
    color: var(--stat-icon-color);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    transition: all var(--transition-speed) ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.stat-card:hover i {
    transform: scale(1.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.recent-activity {
    background: var(--profile-card-bg);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 6px var(--shadow-color);
}

.section-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 10px;
    transition: all var(--transition-speed) ease;
    cursor: pointer;
}

.activity-item:hover {
    background: var(--activity-item-hover);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-title {
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.activity-time {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 1.5rem;
    }

    .profile-meta {
        justify-content: center;
    }

    .profile-actions {
        justify-content: center;
    }

    .profile-image-container {
        width: 120px;
        height: 120px;
        margin-bottom: 1rem;
    }
}
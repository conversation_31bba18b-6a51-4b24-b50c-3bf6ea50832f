
:root[data-theme="light"] {
    --dashboard-bg: #f5f7fa;
    --card-bg: #ffffff;
    --card-border: #e1e1e1;
    --stats-bg: #f8f8f8;
    --action-btn-bg: #7C65B3;
    --action-btn-hover: #9D84D2;
    --header-bg: #ffffff;
    --text-color: #333333;
    --text-secondary: #666666;
    --border-color: #e1e1e1;
    --primary-color: #7C65B3;
    --primary-color-dark: #5D4A8F;
}

:root[data-theme="dark"] {
    --dashboard-bg: #1a1a2e;
    --card-bg: #252541;
    --card-border: #333355;
    --stats-bg: #1E1E32;
    --action-btn-bg: #7C65B3;
    --action-btn-hover: #9D84D2;
    --header-bg: #252541;
    --text-color: #E1E1E6;
    --text-secondary: #A1A1AA;
    --border-color: #333355;
    --primary-color: #9D84D2;
    --primary-color-dark: #7C65B3;
}

.dashboard-layout {
    display: grid;
    grid-template-columns: 260px 1fr;
    min-height: 100vh;
    background: var(--dashboard-bg);
    transition: all 0.3s ease;
}

.main-content {
    padding: 2rem;
    overflow-y: auto;
    color: var(--text-color);
}

.header {
    background: var(--header-bg);
    color: var(--text-color);
    padding: 1rem 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}




/* Quick Actions Section Styles */
.quick-actions .btn {
    background: var(--stats-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    width: 100%;
    justify-content: center;
}

.quick-actions .btn:hover {
    background: var(--card-bg);
    border-color: var(--primary-color);
}

.user-profile {
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.user-profile span {
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-profile img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.welcome-banner {
    background: linear-gradient(135deg, var(--action-btn-bg), var(--action-btn-hover));
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: left;
}

.welcome-banner h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.welcome-banner p {
    opacity: 0.9;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.admin-dashboard {
    padding: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-card .value {
    color: var(--text-color);
    font-size: 1.8rem;
    font-weight: 600;
}

.stat-card .icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.recent-activity {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.activity-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.activity-card h2 {
    color: var(--text-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.activity-list {
    list-style: none;
    padding: 0;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item .name {
    color: var(--text-color);
    font-weight: 500;
}

.activity-item .date {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-header h1 {
    color: var(--text-color);
    font-size: 1.8rem;
    font-weight: 600;
}

.admin-actions {
    display: flex;
    gap: 1rem;
}

.admin-btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
}

.btn-secondary {
    background: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-primary:hover {
    background: var(--primary-color-dark);
}

.btn-secondary:hover {
    background: var(--border-color);
}

.stat-card i {
    font-size: 2rem;
    color: var(--action-btn-bg);
}

.stat-info h3 {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    color: var(--text-color);
}

.stat-info p {
    color: var(--text-secondary);
}

.quick-actions {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 15px;
    padding: 1.5rem;
}

.quick-actions h2 {
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--action-btn-bg);
    color: white;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn:hover {
    background: var(--action-btn-hover);
    transform: translateY(-2px);
}

.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    background: var(--action-btn-bg);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
}

@media (max-width: 768px) {
    .dashboard-layout {
        grid-template-columns: 1fr;
    }

    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .sidebar {
        position: fixed;
        left: -100%;
        top: 0;
        height: 100vh;
        z-index: 999;
        transition: left 0.3s ease;
    }

    .sidebar.active {
        left: 0;
    }

    .main-content {
        padding: 1rem;
    }

    .stats-overview {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }
}



/* Weekly Goals Section Styles */
.weekly-goals {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.goal-item {
    background: var(--stats-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.goal-item.completed {
    background: var(--primary-color-dark);
    border-color: var(--primary-color);
}

.goal-item.completed .goal-text {
    color: var(--text-color);
}

.btn-success {
    background: var(--primary-color);
    border-color: var(--primary-color-dark);
}

.btn-success:hover {
    background: var(--primary-color-dark);
}

.add-goal {
    width: 100%;
    margin-top: 1rem;
    background: var(--action-btn-bg);
    border: none;
}

.add-goal:hover {
    background: var(--action-btn-hover);
}

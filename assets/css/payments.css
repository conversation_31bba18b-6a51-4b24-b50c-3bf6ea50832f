:root[data-theme="light"] {
    --primary-color: #7C65B3;
    --secondary-color: #B3A0E8;
    --accent-color: #65B3A0;
    --background-color: #F8F7FC;
    --card-background: #ffffff;
    --text-color: #333333;
    --text-secondary: #666666;
    --border-color: #e1e1e1;
    --input-background: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

:root[data-theme="dark"] {
    --primary-color: #9D84D2;
    --secondary-color: #6A5B8C;
    --accent-color: #65B3A0;
    --background-color: #1A1A2E;
    --card-background: #252541;
    --text-color: #E1E1E6;
    --text-secondary: #B0B0B0;
    --border-color: #333355;
    --input-background: #1E1E32;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

body {
    background: var(--background-color);
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
}

.app-container {
    display: grid;
    grid-template-columns: 260px 1fr;
    min-height: 100vh;
    background: var(--dashboard-bg);
    transition: all 0.3s ease;
}

.payments-container {
    padding: 2rem;
    overflow-y: auto;
    color: var(--text-color);
    background: var(--dashboard-bg);
}

@media (max-width: 768px) {
    .payments-container {
        margin-left: 0;
    }
}

.payments-title {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.subscription-status {
    margin-bottom: 3rem;
}

.status-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.status-card h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

.subscription-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.detail-item .label {
    font-weight: 500;
    color: var(--text-secondary);
}

.detail-item .value {
    font-weight: 600;
    color: var(--text-color);
}

.subscription-actions {
    grid-column: span 2;
    margin-top: 1rem;
}

.renew-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.renew-btn:hover {
    background: var(--primary-dark);
}

.payment-history h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

.history-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.payment-info {
    display: flex;
    gap: 1rem;
}

.payment-info .date {
    color: var(--text-secondary);
}

.payment-info .amount {
    font-weight: 600;
    color: var(--text-color);
}

.status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

.status.completed {
    background: var(--status-success-bg);
    color: var(--status-success);
}

.status.pending {
    background: var(--status-pending-bg);
    color: var(--status-pending);
}

.status.failed {
    background: var(--status-failed-bg);
    color: var(--status-failed);
}

.no-history {
    color: var(--text-secondary);
    text-align: center;
    padding: 2rem 0;
}

@media (max-width: 768px) {
    .payments-container {
        padding: 1rem;
    }

    .payments-title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .payment-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .history-item {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .status {
        align-self: flex-start;
    }
}
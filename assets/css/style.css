:root[data-theme="light"] {
    --primary-color: #7C65B3;
    --secondary-color: #B3A0E8;
    --accent-color: #65B3A0;
    --background-color: #F8F7FC;
    --card-background: #ffffff;
    --text-color: #333333;
    --text-secondary: #666666;
    --border-color: #e1e1e1;
    --input-background: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

:root[data-theme="dark"] {
    --primary-color: #9D84D2;
    --secondary-color: #6A5B8C;
    --accent-color: #65B3A0;
    --background-color: #1A1A2E;
    --card-background: #252541;
    --text-color: #E1E1E6;
    --text-secondary: #B0B0B0;
    --border-color: #333355;
    --input-background: #1E1E32;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Additional styles for headings */
h1, h2, h3, h4, h5, h6, p {
    color: var(--text-color);
}

/* ... existing reset styles ... */

.auth-container {
    max-width: 400px;
    margin: 50px auto;
    padding: 20px;
}

.auth-card {
    background: var(--card-background);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 6px var(--shadow-color);
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header img {
    width: 120px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--input-background);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(124, 101, 179, 0.2);
}

.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px;
    border-radius: 50%;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    cursor: pointer;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.auth-links {
    text-align: center;
    margin-top: 20px;
}

.auth-links a {
    color: var(--primary-color);
    text-decoration: none;
    margin: 0 10px;
}

.guest-mode {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.btn {
    width: 100%;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-guest {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
} 
/* About page specific styles */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/about-hero.jpg');
    background-size: cover;
    background-position: center;
    padding: 100px 0;
    margin-bottom: 50px;
    text-align: center;
}

.hero-section h1 {
    color: var(--hero-text);
    font-size: 3em;
    margin-bottom: 20px;
}

.mission-content {
    text-align: center;
    margin-bottom: 50px;
}

.mission-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.stat-item {
    background-color: var(--card-background);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5em;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.team-member {
    background-color: var(--card-background);
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
}

.team-member img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.team-member h3 {
    margin: 15px;
    color: var(--text-color);
}

.team-member .role {
    color: var(--primary-color);
    margin: 10px 15px;
    font-weight: bold;
}

.team-member .specialization {
    color: var(--text-secondary);
    margin: 10px 15px 15px;
    font-size: 0.9em;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.value-item {
    background-color: var(--card-background);
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.value-item h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

section {
    margin-bottom: 60px;
}

@media (max-width: 768px) {
    .mission-stats,
    .team-grid,
    .values-grid {
        grid-template-columns: 1fr;
    }
}

/* Theme Variables */
:root[data-theme="light"] {
    --primary-color: #7C65B3;
    --secondary-color: #B3A0E8;
    --accent-color: #65B3A0;
    --background-color: #F8F7FC;
    --card-background: rgba(255, 255, 255, 0.95);
    --text-color: #333333;
    --text-secondary: #666666;
    --border-color: #e1e1e1;
    --input-background: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --error-color: #dc3545;
    --success-color: #28a745;
    --link-color: #7C65B3;
    --link-hover-color: #5A478A;
    --button-text: #ffffff;
    --small-text-color: #777777;
}

:root[data-theme="dark"] {
    --primary-color: #9D84D2;
    --secondary-color: #6A5B8C;
    --accent-color: #65B3A0;
    --background-color: #1A1A2E;
    --card-background: rgba(37, 37, 65, 0.95);
    --text-color: #E1E1E6;
    --text-secondary: #B0B0B0;
    --border-color: #333355;
    --input-background: #1E1E32;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --error-color: #ff4b5a;
    --success-color: #3ce7b1;
    --link-color: #B3A0E8;
    --link-hover-color: #D4C8F5;
    --button-text: #ffffff;
    --small-text-color: #999999;
}

/* Base Styles */
body {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('../images/hi.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    min-height: 100vh;
    margin: 0;
    font-family: 'Arial', sans-serif;
    color: var(--text-color);
}

/* Container and Card */
.auth-container {
    max-width: 400px;
    margin: 50px auto;
    padding: 20px;
    position: relative;
    z-index: 1;
    background: var(--background-color);
    border-radius: 20px;
    box-shadow: 0 8px 32px var(--shadow-color);
}

.auth-card {
    background: linear-gradient(
        135deg,
        var(--card-background),
        rgba(124, 101, 179, 0.05)
    );
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 6px var(--shadow-color);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Header Styles */
.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header img {
    width: 120px;
    margin-bottom: 20px;
}

.auth-header h1 {
    color: var(--text-color);
    margin-bottom: 10px;
    font-size: 24px;
}

.auth-header p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 16px;
}

/* Form Elements */
.form-group {
    margin-bottom: 24px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    color: var(--text-color);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group small {
    display: block;
    color: var(--small-text-color);
    font-size: 12px;
    margin-top: 6px;
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    background: var(--input-background);
    color: var(--text-color);
    transition: all 0.3s ease;
    box-sizing: border-box;
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(124, 101, 179, 0.15);
}

.form-control::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

/* Buttons */
.btn {
    width: 100%;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--button-text);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--button-text);
}

.btn-guest {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

/* Links */
.auth-links {
    text-align: center;
    margin-top: 20px;
}

.auth-links a {
    color: var(--link-color);
    text-decoration: none;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.auth-links a:hover {
    color: var(--link-hover-color);
}

/* Guest Mode Section */
.guest-mode {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.guest-mode p {
    color: var(--text-secondary);
    margin-bottom: 15px;
}

/* Theme Toggle */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px;
    border-radius: 50%;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    cursor: pointer;
    box-shadow: 0 2px 4px var(--shadow-color);
    transition: all 0.3s ease;
    z-index: 1000;
    font-size: 18px;
}

.theme-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

/* Alert Messages */
.alert {
    padding: 12px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-size: 14px;
}

.alert-danger {
    background-color: var(--error-color);
    color: white;
}

.alert-success {
    background-color: var(--success-color);
    color: white;
}

.alert ul {
    margin: 0;
    padding-left: 20px;
}

/* Responsive Design */
@media (max-width: 480px) {
    .auth-container {
        margin: 20px auto;
        padding: 10px;
    }

    .auth-card {
        padding: 20px;
    }

    .auth-header img {
        width: 100px;
    }

    .btn {
        padding: 10px 20px;
    }
}

/* Header Profile Image */
.user-profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
    border: 2px solid var(--primary-color);
}

.user-profile {
    display: flex;
    align-items: center;
    padding: 8px;
    background: var(--card-background);
    border-radius: 25px;
    margin: 10px;
}

.user-profile span {
    color: var(--text-color);
    font-size: 14px;
}

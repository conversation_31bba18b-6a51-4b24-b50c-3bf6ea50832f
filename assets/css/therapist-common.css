/* Payment Sections */
.table-container {
    overflow-x: auto;
    margin: 1rem 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--surface-color);
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
}

tr:hover {
    background-color: var(--hover-color);
}

.status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status.completed {
    background-color: var(--success-color);
    color: white;
}

.status.pending {
    background-color: var(--warning-color);
    color: var(--text-color);
}

.status.failed {
    background-color: var(--error-color);
    color: white;
}

.no-data {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
    font-style: italic;
}

.payment-settings {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.payment-settings h2 {
    margin-bottom: 1.5rem;
    color: var(--text-color);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-prefix {
    position: absolute;
    left: 1rem;
    color: var(--text-muted);
}

.input-group input {
    padding-left: 2.5rem;
}

.payment-method-settings {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: var(--background-color);
    border-radius: 4px;
}

.mpesa-info {
    margin-top: 1rem;
    padding: 1rem;
    background-color: var(--info-color);
    color: white;
    border-radius: 4px;
}

.mpesa-info p {
    margin: 0.5rem 0;
}

.form-text {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* Dark theme adjustments */
[data-theme="dark"] .table-container {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] th {
    background-color: var(--primary-dark);
}

[data-theme="dark"] .payment-settings {
    background-color: var(--surface-dark);
}

[data-theme="dark"] .payment-method-settings {
    background-color: var(--background-dark);
}

[data-theme="dark"] .mpesa-info {
    background-color: var(--info-dark);
}

/* Enhanced Profile Page Styles */
.profile-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--surface-color);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.profile-image-container {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: 4px solid var(--primary-color);
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-upload {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    padding: 0.5rem;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.profile-image-container:hover .file-upload {
    opacity: 1;
}

.file-upload-label {
    color: white;
    font-size: 0.875rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.file-upload input[type="file"] {
    display: none;
}

.profile-info {
    flex: 1;
}

.profile-info h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.profile-info p {
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.profile-stats {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Tabs Styling */
.tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
    overflow-x: auto;
    scrollbar-width: thin;
}

.tab {
    padding: 0.75rem 1.5rem;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-muted);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab:hover {
    color: var(--primary-color);
    background-color: var(--hover-color);
}

.tab.active {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-bottom: 2px solid var(--primary-color);
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Form Styling */
.profile-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--input-background);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
    outline: none;
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
}

/* Profile Section Styling */
.profile-section {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.profile-section h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.75rem;
}

.profile-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.profile-item {
    background-color: var(--background-color);
    border-radius: 8px;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-item h3 {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
}

.profile-item p {
    color: var(--text-color);
    line-height: 1.6;
}

/* Review Styling */
.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.rating {
    color: #ffc107;
    font-size: 1.25rem;
}

.review-date {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.review-text {
    font-style: italic;
    line-height: 1.6;
}

.reviewer {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Button Styling */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--hover-color);
}

/* Alert Styling */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
    }
    
    .profile-stats {
        justify-content: center;
    }
    
    .tabs {
        padding-bottom: 0.5rem;
    }
    
    .tab {
        padding: 0.5rem 1rem;
    }
    
    .profile-grid {
        grid-template-columns: 1fr;
    }
}

/* Dark Theme Adjustments */
[data-theme="dark"] .profile-header {
    background-color: var(--surface-dark);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .profile-image-container {
    border-color: var(--primary-dark);
}

[data-theme="dark"] .profile-item {
    background-color: var(--background-dark);
}

[data-theme="dark"] .alert-success {
    background-color: rgba(40, 167, 69, 0.2);
    color: #d4edda;
}

[data-theme="dark"] .alert-error {
    background-color: rgba(220, 53, 69, 0.2);
    color: #f8d7da;
} 
.payment-section {
    padding: 40px 0;
    max-width: 1000px;
    margin: 0 auto;
}

.payment-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    background: var(--bg-secondary);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.plan-summary {
    padding: 20px;
    background: var(--bg-primary);
    border-radius: 8px;
}

.plan-selector {
    margin-bottom: 20px;
}

.plan-selector label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.plan-selector select {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 16px;
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: border-color 0.3s;
}

.plan-selector select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.plan-price {
    margin: 20px 0;
    text-align: center;
}

.plan-price .amount {
    font-size: 2.5em;
    font-weight: bold;
    color: var(--text-primary);
}

.plan-price .period {
    font-size: 1.2em;
    color: var(--text-secondary);
}

.plan-features {
    margin-top: 20px;
}

.feature-item {
    padding: 10px 0;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.payment-form {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: var(--text-secondary);
}

.payment-info {
    margin: 20px 0;
    padding: 15px;
    background: var(--bg-accent);
    border-radius: 6px;
}

.payment-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.payment-info li {
    margin: 5px 0;
    color: var(--text-secondary);
}

.btn-payment {
    width: 100%;
    padding: 15px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 18px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-payment:hover {
    background: var(--primary-dark);
}

@media (max-width: 768px) {
    .payment-details {
        grid-template-columns: 1fr;
    }
}
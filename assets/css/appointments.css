/* Appointments Booking Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 2% auto;
    padding: 25px;
    border-radius: 10px;
    width: 85%;
    max-width: 1200px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-20px);}
    to {opacity: 1; transform: translateY(0);}
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    color: #333;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
}

.close:hover {
    color: #007bff;
}

.modal-title {
    text-align: center;
    color: #333;
    font-size: 28px;
    margin-bottom: 20px;
    font-weight: 600;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 15px;
}

.appointments-container {
    padding: 20px;
}

.booking-flow {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.therapist-selection {
    flex: 1;
    min-width: 300px;
}

.appointment-form {
    flex: 2;
    min-width: 500px;
}

.section-title {
    font-size: 20px;
    color: #333;
    margin-bottom: 15px;
    font-weight: 500;
    border-left: 4px solid #007bff;
    padding-left: 10px;
}

/* Therapist Grid */
.therapist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.therapist-card {
    border-radius: 8px;
    padding: 20px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.therapist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.therapist-card.selected {
    background-color: #e8f4ff;
    border: 2px solid #007bff;
}

.therapist-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.therapist-card h4 {
    font-size: 18px;
    margin: 10px 0 5px;
    color: #333;
}

.specialization {
    color: #666;
    font-style: italic;
    margin-bottom: 5px;
}

.experience {
    font-size: 14px;
    color: #555;
    margin-bottom: 10px;
}

.rating {
    color: #ffb400;
    margin-top: 5px;
}

.rating span {
    color: #777;
    font-size: 14px;
}

/* Appointment Type Options */
.appointment-type-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 25px;
}

.type-option {
    flex: 1;
    min-width: 150px;
    padding: 15px;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.type-option:hover {
    background-color: #f0f7ff;
}

.type-option.selected {
    background-color: #e8f4ff;
    border: 2px solid #007bff;
}

.type-option h4 {
    margin-top: 0;
    color: #333;
}

.type-option .price {
    font-weight: bold;
    color: #007bff;
    font-size: 18px;
    margin-top: 10px;
}

/* Calendar */
.calendar-section {
    margin-bottom: 25px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.calendar-header h4 {
    margin: 0;
}

.calendar-header button {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #007bff;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.weekday {
    text-align: center;
    font-weight: bold;
    padding: 10px 0;
    background-color: #f0f0f0;
    border-radius: 5px;
}

.calendar-day {
    text-align: center;
    padding: 10px 0;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.calendar-day:hover:not(.empty):not(.past) {
    background-color: #e8f4ff;
}

.calendar-day.selected {
    background-color: #007bff;
    color: white;
}

.calendar-day.today {
    border: 2px solid #007bff;
}

.calendar-day.past {
    color: #ccc;
    cursor: not-allowed;
}

.calendar-day.empty {
    background-color: transparent;
}

/* Time Slots */
.time-slots {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.time-slot {
    padding: 8px 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.time-slot:hover {
    background-color: #e8f4ff;
}

.time-slot.selected {
    background-color: #007bff;
    color: white;
}

.loading, .no-slots, .error {
    padding: 15px;
    text-align: center;
    width: 100%;
}

.no-slots {
    color: #666;
    font-style: italic;
}

.error {
    color: #dc3545;
}

/* Booking Summary */
.booking-summary {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-title {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 5px 0;
}

.summary-item .label {
    font-weight: bold;
    color: #555;
}

.summary-item .value {
    color: #333;
}

.total-amount {
    border-top: 2px solid #ddd;
    padding-top: 10px;
    margin-top: 10px;
    font-size: 18px;
}

.total-amount .value {
    color: #007bff;
    font-weight: bold;
}

.btn-book {
    display: block;
    width: 100%;
    padding: 12px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-top: 20px;
}

.btn-book:hover:not([disabled]) {
    background-color: #0056b3;
}

.btn-book:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        padding: 15px;
    }
    
    .booking-flow {
        flex-direction: column;
    }
    
    .therapist-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
    
    .appointment-type-options {
        flex-direction: column;
    }
    
    .type-option {
        width: 100%;
    }
}
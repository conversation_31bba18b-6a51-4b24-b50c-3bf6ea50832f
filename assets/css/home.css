/* Theme Variables - Consolidated from all files */
:root[data-theme="light"] {
    --primary-color: #7C65B3;
    --secondary-color: #B3A0E8;
    --accent-color: #65B3A0;
    --background-color: #F8F7FC;
    --card-background: #ffffff;
    --text-color: #333333;
    --text-secondary: #666666;
    --border-color: #e1e1e1;
    --input-background: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --nav-background: rgba(255, 255, 255, 0.95);
    --nav-text: #333333;
    --nav-hover: #7C65B3;
    --footer-background: #f5f5f5;
    --button-primary: #7C65B3;
    --button-hover: #6A5B8C;
    --card-hover: #f8f8f8;
    --hero-text: #ffffff;
    --error-color: #dc3545;
    --success-color: #28a745;
}

:root[data-theme="dark"] {
    --primary-color: #9D84D2;
    --secondary-color: #6A5B8C;
    --accent-color: #65B3A0;
    --background-color: #1A1A2E;
    --card-background: #252541;
    --text-color: #E1E1E6;
    --text-secondary: #B0B0B0;
    --border-color: #333355;
    --input-background: #1E1E32;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --nav-background: rgba(26, 26, 46, 0.95);
    --nav-text: #E1E1E6;
    --nav-hover: #B3A0E8;
    --footer-background: #1E1E32;
    --button-primary: #9D84D2;
    --button-hover: #B3A0E8;
    --card-hover: #2A2A4A;
    --hero-text: #ffffff;
    --error-color: #ff4b5a;
    --success-color: #3ce7b1;
}

/* Base styles */
body {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* Header Styles */
.header {
    background-color: var(--nav-background);
    padding: 1rem 2rem;
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1000;
    box-shadow: 0 2px 5px var(--shadow-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header .brand {
    display: flex;
    align-items: center;
}

.header .logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.header .navgation ul {
    list-style: none;
    display: flex;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.header .navgation ul li a {
    color: var(--nav-text);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.header .navgation ul li a:hover {
    background-color: var(--nav-hover);
    color: var(--hero-text);
}

/* Theme Toggle Button */
.theme-toggle {
    background: transparent;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

/* Main Content Styles */
main {
    margin-top: 80px;
    padding: 2rem;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.hero {
    background-color: var(--card-background);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px var(--shadow-color);
    margin-bottom: 2rem;
}

.hero h1 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.hero p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

/* Form Styles */
form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

input, textarea {
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-background);
    color: var(--text-color);
}

button {
    padding: 0.8rem 1.5rem;
    background-color: var(--button-primary);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: var(--button-hover);
}

/* Footer Styles */
footer {
    background-color: var(--footer-background);
    color: var(--text-color);
    padding: 2rem;
    margin-top: 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-content h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.footer-content p {
    color: var(--text-secondary);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        padding: 1rem;
    }

    .header .navgation ul {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .container {
        padding: 1rem;
    }
}

/* Add these new styles to your existing home.css */

.hero-banner {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    padding: 120px 0 80px;
    text-align: center;
    color: var(--hero-text);
}

.hero-content h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
}

.hero-subtitle {
    font-size: 1.2em;
    margin-bottom: 30px;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-secondary {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin: 60px auto;
}

.feature-card {
    background-color: var(--card-background);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 2.5em;
    margin-bottom: 20px;
}

.slide-image {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
    border-radius: 10px;
}

.slide-caption {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: center;
    border-radius: 0 0 10px 10px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.service-card {
    background-color: var(--card-background);
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.service-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.service-card h3,
.service-card p {
    padding: 15px;
    margin: 0;
}

.learn-more {
    display: inline-block;
    padding: 10px 15px;
    color: var(--primary-color);
    text-decoration: none;
    margin: 10px 15px;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin: 40px 0;
}

.testimonial-card {
    background-color: var(--card-background);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
}

.quote {
    font-style: italic;
    margin-bottom: 20px;
}

.author {
    color: var(--primary-color);
    font-weight: bold;
}

.cta-section {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: 60px 0;
    margin: 60px 0 0 0;
}

.btn-large {
    font-size: 1.2em;
    padding: 15px 30px;
}

.social-links {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.social-links a {
    font-size: 1.5em;
    color: var(--text-color);
    text-decoration: none;
}

@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2em;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .slide-image {
        max-height: 300px;
    }
}

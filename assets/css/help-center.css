:root[data-theme="light"] {
    --help-card-bg: #ffffff;
    --help-border: #e1e1e1;
    --search-bg: #f8f8f8;
    --category-hover: #f5f5f5;
    --text-color: #333333;
    --text-secondary: #666666;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --primary-color: #7C65B3;
    --button-hover: #9D84D2;
}

:root[data-theme="dark"] {
    --help-card-bg: #252541;
    --help-border: #333355;
    --search-bg: #1E1E32;
    --category-hover: #2A2A4A;
    --text-color: #ffffff;
    --text-secondary: #cccccc;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --primary-color: #9D84D2;
    --button-hover: #7C65B3;
}

.help-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 3rem 2rem 3rem 4rem;
}

.help-header {
    text-align: center;
    margin-bottom: 4rem;
    margin-left: 2rem;
    background: linear-gradient(135deg, var(--primary-color) 20%, transparent 80%);
    background-size: 200% auto;
    padding: 2rem 1rem;
    border-radius: 15px;
    animation: headerGlow 8s ease infinite;
}

.help-title {
    font-size: 2.5rem;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.help-description {
    color: var(--text-secondary);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.search-container {
    margin-bottom: 4rem;
}

.search-box {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid var(--help-border);
    border-radius: 12px;
    background: var(--search-bg);
    color: var(--text-color);
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.search-box:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(124, 101, 179, 0.2);
}

.help-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.category-card {
    background: var(--help-card-bg);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid var(--help-border);
}

.category-card:hover {
    background: var(--category-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.category-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: white;
    font-size: 1.5rem;
    animation: float 3s ease-in-out infinite;
    box-shadow: 0 4px 15px rgba(124, 101, 179, 0.3);
}

.category-title {
    font-size: 1.2rem;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.category-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

.faq-section {
    background: var(--help-card-bg);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.faq-item {
    border-bottom: 1px solid var(--help-border);
    padding: 2rem 0;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-question {
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 1rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
}

.faq-question::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.faq-question:hover::after,
.faq-item.active .faq-question::after {
    width: 100%;
}

.faq-question:hover {
    color: var(--primary-color);
}

.faq-answer {
    color: var(--text-secondary);
    line-height: 1.6;
    padding-left: 1rem;
    border-left: 3px solid var(--primary-color);
    margin-top: 1rem;
    display: none;
}

.faq-item.active .faq-answer {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes headerGlow {
    0% { background-position: 100% 50%; }
    50% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
    100% { transform: translateY(0px); }
}

.contact-support {
    text-align: center;
    margin-top: 4rem;
    padding: 3rem 2rem;
    background: var(--help-card-bg);
    border-radius: 20px;
}

.contact-title {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.contact-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.contact-btn {
    background: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
    background: var(--button-hover);
}

@media (max-width: 768px) {
    .help-title {
        font-size: 2rem;
    }

    .help-categories {
        grid-template-columns: 1fr;
    }

    .category-card {
        text-align: center;
    }

    .category-icon {
        margin: 0 auto 1rem;
    }
}
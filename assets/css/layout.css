:root {
    --header-height: 60px;
}

body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.app-container {
    display: flex;
    flex: 1;
    position: relative;
}

.main-content {
    flex: 1;
    margin-left: 260px; /* Same as sidebar width */
    min-height: calc(100vh - var(--header-height));
    overflow-y: auto;
    background-color: var(--resource-bg);
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        width: 100%;
    }
}
/* Resources Page Styles */
:root {
    /* Light theme variables */
    --resource-bg: #ffffff;
    --resource-card-bg: #f8f9fa;
    --resource-border: #e9ecef;
    --resource-text: #212529;
    --resource-muted: #6c757d;
    --resource-primary: #65B3A0;
    --resource-secondary: #B3A0E8;
    --resource-success: #28a745;
    --resource-danger: #dc3545;
    --resource-warning: #ffc107;
    --resource-info: #17a2b8;
    
    /* Resource category colors */
    --category-self-help: #65B3A0;
    --category-video: #B3A0E8;
    --category-support: #E88A72;
    --category-article: #72A0E8;
    --category-exercise: #E87272;
    --category-meditation: #E8B372;
    --category-education: #A0A0A0;
    --category-emergency: #72E872;
}

/* Dark theme variables */
[data-theme="dark"] {
    --resource-bg: #1a1a1a;
    --resource-card-bg: #2d2d2d;
    --resource-border: #404040;
    --resource-text: #ffffff;
    --resource-muted: #a0a0a0;
}

/* Resources Container */
.resources-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background-color: var(--resource-bg);
    color: var(--resource-text);
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--resource-text);
    margin: 0;
}

.search-bar {
    position: relative;
    width: 300px;
}

.search-bar input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--resource-border);
    border-radius: 0.5rem;
    background-color: var(--resource-card-bg);
    color: var(--resource-text);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: var(--resource-primary);
    box-shadow: 0 0 0 3px rgba(101, 179, 160, 0.2);
}

.search-bar i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--resource-muted);
}

/* Resources Grid */
.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.resource-category {
    background-color: var(--resource-card-bg);
    border: 1px solid var(--resource-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.resource-category:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.resource-category h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--resource-text);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.resource-category h2 i {
    color: var(--resource-primary);
}

.resource-list {
    display: grid;
    gap: 1rem;
}

.resource-card {
    display: flex;
    flex-direction: column;
    background-color: var(--resource-bg);
    border: 1px solid var(--resource-border);
    border-radius: 0.5rem;
    padding: 1.25rem;
    text-decoration: none;
    color: var(--resource-text);
    transition: all 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--resource-primary);
}

.resource-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--resource-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.resource-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.resource-card p {
    color: var(--resource-muted);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* Emergency Resources */
.emergency-resources {
    background-color: var(--resource-danger);
    color: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 2rem;
}

.emergency-resources h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.emergency-contacts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.emergency-card {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    padding: 1.25rem;
}

.emergency-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.emergency-card p {
    margin: 0;
    font-size: 0.9rem;
}

.emergency-card strong {
    font-size: 1.1rem;
    font-weight: 700;
}

/* Resource Filters */
.resource-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background-color: var(--resource-card-bg);
    border: 1px solid var(--resource-border);
    border-radius: 2rem;
    color: var(--resource-text);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--resource-primary);
    color: white;
    border-color: var(--resource-primary);
}

/* Resource Modal */
.resource-modal .modal-content {
    max-width: 800px;
}

.resource-detail {
    margin-bottom: 2rem;
}

.resource-header {
    margin-bottom: 2rem;
}

.resource-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--resource-text);
    margin-bottom: 0.5rem;
}

.resource-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--resource-muted);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.resource-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.resource-body {
    display: grid;
    gap: 2rem;
}

.resource-image {
    width: 100%;
    max-height: 400px;
    object-fit: cover;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.resource-content {
    line-height: 1.7;
    color: var(--resource-text);
}

.resource-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 1.5rem 0 0.75rem;
}

.resource-content p {
    margin-bottom: 1rem;
}

.resource-content ul,
.resource-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.resource-content li {
    margin-bottom: 0.5rem;
}

.resource-download {
    margin-top: 2rem;
    text-align: center;
}

.resource-download .btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--resource-primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.resource-download .btn-primary:hover {
    background-color: var(--resource-secondary);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    background-color: var(--resource-card-bg);
    border: 1px solid var(--resource-border);
    border-radius: 0.75rem;
}

.empty-state i {
    font-size: 3rem;
    color: var(--resource-muted);
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: var(--resource-text);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--resource-muted);
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .resources-container {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .search-bar {
        width: 100%;
    }
    
    .resources-grid {
        grid-template-columns: 1fr;
    }
    
    .emergency-contacts {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.resource-card {
    animation: fadeIn 0.3s ease forwards;
}

.resource-card:nth-child(2) {
    animation-delay: 0.1s;
}

.resource-card:nth-child(3) {
    animation-delay: 0.2s;
}

.resource-card:nth-child(4) {
    animation-delay: 0.3s;
}
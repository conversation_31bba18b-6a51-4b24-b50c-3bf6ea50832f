/* Journal Styles */
:root {
    /* Light theme variables */
    --journal-bg: #ffffff;
    --journal-card-bg: #f8f9fa;
    --journal-border: #e9ecef;
    --journal-text: #212529;
    --journal-muted: #6c757d;
    --journal-primary: #65B3A0;
    --journal-secondary: #B3A0E8;
    --journal-success: #28a745;
    --journal-danger: #dc3545;
    --journal-warning: #ffc107;
    --journal-info: #17a2b8;
    
    /* Mood colors */
    --mood-happy: #65B3A0;
    --mood-calm: #B3A0E8;
    --mood-anxious: #E88A72;
    --mood-sad: #72A0E8;
    --mood-angry: #E87272;
    --mood-excited: #E8B372;
    --mood-tired: #A0A0A0;
    --mood-energetic: #72E872;
    
    /* Resource colors */
    --resource-bg: #ffffff;
    --resource-card-bg: #f8f9fa;
    --resource-border: #e9ecef;
    --resource-text: #212529;
    --resource-muted: #6c757d;
    --resource-primary: #65B3A0;
    --resource-secondary: #B3A0E8;
    
    /* Notification colors */
    --notification-bg: #ffffff;
    --notification-border: #e9ecef;
    --notification-text: #212529;
    --notification-muted: #6c757d;
    --notification-primary: #65B3A0;
    --notification-secondary: #B3A0E8;
    --notification-success: #28a745;
    --notification-danger: #dc3545;
    --notification-warning: #ffc107;
    --notification-info: #17a2b8;
}

/* Dark theme variables */
[data-theme="dark"] {
    --journal-bg: #1a1a1a;
    --journal-card-bg: #2d2d2d;
    --journal-border: #404040;
    --journal-text: #ffffff;
    --journal-muted: #a0a0a0;
    --journal-primary: #65B3A0;
    --journal-secondary: #B3A0E8;
    
    --resource-bg: #1a1a1a;
    --resource-card-bg: #2d2d2d;
    --resource-border: #404040;
    --resource-text: #ffffff;
    --resource-muted: #a0a0a0;
    
    --notification-bg: #1a1a1a;
    --notification-border: #404040;
    --notification-text: #ffffff;
    --notification-muted: #a0a0a0;
}

/* Journal Container */
.journal-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    background-color: var(--journal-bg);
    color: var(--journal-text);
}

/* Journal Header */
.journal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.journal-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--journal-text);
}

.new-entry-btn {
    padding: 0.75rem 1.5rem;
    background-color: var(--journal-primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.new-entry-btn:hover {
    background-color: var(--journal-secondary);
}

/* Journal Entries */
.journal-entries {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.journal-card {
    background-color: var(--journal-card-bg);
    border: 1px solid var(--journal-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.journal-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.entry-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.entry-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--journal-muted);
    font-size: 0.875rem;
}

.entry-mood {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
}

.entry-content {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: var(--journal-text);
}

.entry-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--journal-muted);
    cursor: pointer;
    transition: color 0.3s ease;
}

.btn-icon:hover {
    color: var(--journal-primary);
}

/* Mood Selection */
.mood-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.mood-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background-color: var(--journal-card-bg);
    border: 1px solid var(--journal-border);
    border-radius: 0.5rem;
    color: var(--journal-text);
    cursor: pointer;
    transition: all 0.3s ease;
}

.mood-btn:hover {
    background-color: var(--journal-primary);
    color: white;
    border-color: var(--journal-primary);
}

.mood-btn.active {
    background-color: var(--journal-primary);
    color: white;
    border-color: var(--journal-primary);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--journal-bg);
    border-radius: 0.75rem;
    padding: 2rem;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--journal-muted);
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--journal-text);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--journal-text);
    font-weight: 500;
}

.form-group textarea {
    width: 100%;
    padding: 1rem;
    background-color: var(--journal-card-bg);
    border: 1px solid var(--journal-border);
    border-radius: 0.5rem;
    color: var(--journal-text);
    resize: vertical;
    min-height: 200px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-primary {
    padding: 0.75rem 1.5rem;
    background-color: var(--journal-primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--journal-secondary);
}

.btn-secondary {
    padding: 0.75rem 1.5rem;
    background-color: var(--journal-card-bg);
    color: var(--journal-text);
    border: 1px solid var(--journal-border);
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: var(--journal-border);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    background-color: var(--journal-card-bg);
    border: 1px solid var(--journal-border);
    border-radius: 0.75rem;
}

.empty-state i {
    font-size: 3rem;
    color: var(--journal-muted);
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: var(--journal-text);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--journal-muted);
    margin-bottom: 1.5rem;
}

/* Toast Notifications */
.toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    background-color: var(--notification-bg);
    border: 1px solid var(--notification-border);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(100%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.success-toast {
    border-left: 4px solid var(--notification-success);
}

.error-toast {
    border-left: 4px solid var(--notification-danger);
}

.toast i {
    font-size: 1.25rem;
}

.success-toast i {
    color: var(--notification-success);
}

.error-toast i {
    color: var(--notification-danger);
}

/* Journal Insights */
.insight-modal .modal-content {
    max-width: 800px;
}

.insight-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.insight-header i {
    font-size: 2rem;
    color: var(--journal-primary);
}

.insight-content {
    margin-bottom: 2rem;
}

.insight-data {
    background-color: var(--journal-card-bg);
    border: 1px solid var(--journal-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.insight-stat {
    margin-bottom: 1.5rem;
}

.insight-stat:last-child {
    margin-bottom: 0;
}

.insight-stat h4 {
    color: var(--journal-text);
    margin-bottom: 1rem;
}

.mood-trend-chart {
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
    height: 200px;
    padding: 1rem 0;
}

.mood-bar {
    flex: 1;
    min-width: 30px;
    border-radius: 4px;
    transition: height 0.3s ease;
}

.theme-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.theme-list li {
    padding: 0.5rem 1rem;
    background-color: var(--journal-bg);
    border: 1px solid var(--journal-border);
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--journal-text);
}

/* Resources */
.resources-container {
    margin-top: 3rem;
}

.resources-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.resources-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--journal-text);
}

.resources-filters {
    display: flex;
    gap: 1rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background-color: var(--journal-card-bg);
    border: 1px solid var(--journal-border);
    border-radius: 0.5rem;
    color: var(--journal-text);
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--journal-primary);
    color: white;
    border-color: var(--journal-primary);
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.resource-card {
    background-color: var(--resource-card-bg);
    border: 1px solid var(--resource-border);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.resource-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.resource-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.resource-content {
    padding: 1.5rem;
}

.resource-type {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--resource-primary);
    color: white;
    border-radius: 2rem;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.resource-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--resource-text);
    margin-bottom: 0.5rem;
}

.resource-description {
    color: var(--resource-muted);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.resource-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.resource-tag {
    padding: 0.25rem 0.75rem;
    background-color: var(--resource-card-bg);
    border: 1px solid var(--resource-border);
    border-radius: 2rem;
    font-size: 0.875rem;
    color: var(--resource-text);
}

.resource-actions {
    display: flex;
    justify-content: flex-end;
}

.action-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-action {
    background-color: var(--resource-primary);
    color: white;
    border: none;
}

.primary-action:hover {
    background-color: var(--resource-secondary);
}

/* Resource Modal */
.resource-modal .modal-content {
    max-width: 800px;
}

.resource-detail {
    margin-bottom: 2rem;
}

.resource-header {
    margin-bottom: 2rem;
}

.resource-body {
    display: grid;
    gap: 2rem;
}

.resource-download {
    margin-top: 2rem;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .journal-container {
        padding: 1rem;
    }
    
    .journal-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .mood-options {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .resources-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .resources-filters {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .resources-grid {
        grid-template-columns: 1fr;
    }
    
    .toast {
        left: 1rem;
        right: 1rem;
        bottom: 1rem;
    }
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(100%);
        opacity: 0;
    }
}

.toast.show {
    animation: slideIn 0.3s ease forwards;
}

.toast.hide {
    animation: slideOut 0.3s ease forwards;
}
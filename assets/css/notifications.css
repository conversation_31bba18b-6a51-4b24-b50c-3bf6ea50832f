:root[data-theme="light"] {
    --notification-card-bg: #ffffff;
    --notification-border: #e1e1e1;
    --notification-hover: #f5f5f5;
    --notification-unread: #f0f7ff;
    --notification-dot: #ff3e3e;
    --notification-icon-bg: #f0f0f0;
    --notification-time: #666666;
    --notification-type-appointment: #4CAF50;
    --notification-type-message: #2196F3;
    --notification-type-system: #FF9800;
}

:root[data-theme="dark"] {
    --notification-card-bg: #252541;
    --notification-border: #333355;
    --notification-hover: #2A2A4A;
    --notification-unread: #1E2A3D;
    --notification-dot: #ff5252;
    --notification-icon-bg: #2A2A4A;
    --notification-time: #B0B0B0;
    --notification-type-appointment: #81C784;
    --notification-type-message: #64B5F6;
    --notification-type-system: #FFB74D;
}

.notifications-container {
    width: 100%;
    padding: 1.5rem;
    background: var(--dashboard-bg);
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: var(--notification-card-bg);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notifications-title {
    font-size: 1.8rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mark-all-read {
    color: var(--primary-color);
    background: var(--notification-icon-bg);
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mark-all-read:hover {
    background: var(--notification-hover);
    transform: translateY(-1px);
}

.notification-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.notification-item {
    background: var(--notification-card-bg);
    border: 1px solid var(--notification-border);
    border-radius: 12px;
    padding: 1.25rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
    background: var(--notification-unread);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-icon.appointment {
    background: var(--notification-type-appointment);
    color: white;
}

.notification-icon.message {
    background: var(--notification-type-message);
    color: white;
}

.notification-icon.system {
    background: var(--notification-type-system);
    color: white;
}

.notification-icon.email {
    background: var(--notification-type-email, #4A90E2);
    color: white;
}

.notification-type {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: var(--notification-tag-bg, rgba(74, 144, 226, 0.1));
    border-radius: 4px;
    font-size: 0.8rem;
    color: var(--notification-tag-color, #4A90E2);
}

.notification-content {
    flex: 1;
}

.notification-message {
    color: var(--text-secondary);
    margin: 0.5rem 0;
    line-height: 1.5;
}

.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 0.75rem;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.85rem;
    color: var(--notification-time);
}

.notification-time {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.notification-sender {
    font-weight: 500;
}

.unread-dot {
    width: 8px;
    height: 8px;
    background: var(--notification-dot);
    border-radius: 50%;
    position: absolute;
    top: 1.25rem;
    right: 1.25rem;
}

@media (max-width: 768px) {
    .notifications-container {
        padding: 1rem;
    }

    .notification-item {
        padding: 1rem;
    }

    .notifications-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .notifications-title {
        font-size: 1.5rem;
    }

    .mark-all-read {
        width: 100%;
        justify-content: center;
    }
}
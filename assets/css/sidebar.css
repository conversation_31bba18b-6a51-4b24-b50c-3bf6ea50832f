:root[data-theme="light"] {
    --sidebar-bg: #ffffff;
    --sidebar-text: #333333;
    --sidebar-hover: #f5f5f5;
    --sidebar-active: #7C65B3;
    --sidebar-border: #e1e1e1;
}

:root[data-theme="dark"] {
    --sidebar-bg: #252541;
    --sidebar-text: #E1E1E6;
    --sidebar-hover: #2A2A4A;
    --sidebar-active: #9D84D2;
    --sidebar-border: #333355;
}

.sidebar {
    background: var(--sidebar-bg);
    width: 260px;
    height: 100vh;
    padding: 2rem 1rem;
    border-right: 1px solid var(--sidebar-border);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.sidebar nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--sidebar-text);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-item:hover {
    background: var(--sidebar-hover);
}

.nav-item.active {
    background: var(--sidebar-active);
    color: white;
}

.nav-item i {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.sidebar-footer {
    border-top: 1px solid var(--sidebar-border);
    padding-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.logout-btn {
    background: var(--sidebar-active);
    color: white;
}

@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        top: 0;
        z-index: 999;
        transition: left 0.3s ease;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    }

    .sidebar.active {
        left: 0;
    }
}
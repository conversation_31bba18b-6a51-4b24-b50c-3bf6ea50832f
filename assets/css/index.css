:root[data-theme="light"] {
    --primary-color: #f7f7f7;
    --secondary-color: #9D84D2;
    --accent-color: #65B3A0;
    --background-color: #FFFFFF; 
    --card-background: #F8F8F8; 
    --text-color: #333333; 
    --text-secondary: #555555; 
    --border-color: #DDDDDD; 
    --input-background: #FFFFFF;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --table-hover: #F0F0F0; 
    --nav-hover: #E0E0E0;
    --button: #007BFF;
}

:root[data-theme="dark"] {
    --primary-color: #9D84D2;
    --secondary-color: #6A5B8C;
    --accent-color: #65B3A0;
    --background-color: #1A1A2E;
    --card-background: #252541;
    --text-color: #E1E1E6;
    --text-secondary: #B0B0B0;
    --border-color: #333355;
    --input-background: #1E1E32;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --table-hover: #2A2A4A;
    --nav-hover: #2F2F4F;
    --button: #0040ff;
}

body {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
}

header {
    background-color: var(--background-color); 
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header .brand {
    font-size: 24px;
    font-weight: bold;
}

.header .navgation ul {
    list-style: none;
    display: flex;
    gap: 20px;
    margin: 0;
    padding: 0;
}

.header .navgation ul li {
    display: inline;
}

.header .navgation ul li a {
    text-decoration: none;
    color: var(--text-color);
    padding: 10px 15px;
    transition: background-color 0.3s;
}

.header .navgation ul li a:hover {
    background-color: var(--nav-hover);
    border-radius: 5px;
}

.header .logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

footer {
    background-color: var(--background-color);
    padding: 20px;
    color: var(--text-color);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.footer-content div {
    flex: 1;
    min-width: 200px;
    margin: 10px;
}

.footer-content h3 {
    margin-bottom: 10px;
}

.footer-content p {
    margin: 5px 0;
}

main {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

.container {
    width: 80%;
    max-width: 1200px;
    margin: 20px 0;
    padding: 20px;
    background-color: var(--card-background);
    border-radius: 10px;
    box-shadow: 0 4px 8px var(--shadow-color);
}

.hero-welcome {
    padding: 4rem 2rem;
    background: var(--background-color);
}

.welcome-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.welcome-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-color);
    line-height: 1.2;
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.journey-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.journey-card {
    background: var(--card-background);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px var(--shadow-color);
    transition: transform 0.3s ease;
}

.journey-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.benefits-list li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.welcome-image {
    position: relative;
}

.main-image {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 6px 12px var(--shadow-color);
}

.stats-overlay {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--card-background);
    padding: 1rem 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px var(--shadow-color);
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.quick-access {
    text-align: center;
    margin-top: 2rem;
}

.quick-access a {
    color: var(--primary-color);
    text-decoration: none;
}

.features-section {
    padding: 4rem 2rem;
    background: var(--background-alt);
}

.features-section h2 {
    text-align: center;
    margin-bottom: 3rem;
}

.features-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
    background: var(--card-background);
    border-radius: 15px;
    box-shadow: 0 4px 6px var(--shadow-color);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .welcome-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .journey-cards {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .stats-overlay {
        position: static;
        transform: none;
        margin-top: 1rem;
    }
}

.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/contact-hero.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    text-align: center;
    padding: 100px 0;
    margin-bottom: 50px;
}

.hero-section h1 {
    font-size: 3em;
    margin-bottom: 20px;
    color: white;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    margin-bottom: 60px;
}

.contact-info h2,
.contact-form h2 {
    margin-bottom: 20px;
    color: var(--text-color);
}

.contact-methods {
    margin: 40px 0;
}

.contact-method {
    margin-bottom: 30px;
}

.contact-method .icon {
    font-size: 2em;
    margin-bottom: 10px;
}

.contact-method h3 {
    color: var(--text-color);
    margin-bottom: 5px;
}

.contact-method p {
    color: var(--text-secondary);
}

.social-links h3 {
    margin-bottom: 15px;
    color: var(--text-color);
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icon {
    color: var(--text-color);
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 20px;
    background-color: var(--card-background);
    transition: background-color 0.3s ease;
}

.social-icon:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--input-background);
    color: var(--text-color);
}

.form-group textarea {
    resize: vertical;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
}

.btn-submit {
    background-color: var(--primary-color);
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 100%;
}

.btn-submit:hover {
    background-color: var(--primary-color-dark);
}

/* FAQ Section */
.faq-section {
    margin-bottom: 60px;
}

.faq-section h2 {
    text-align: center;
    margin-bottom: 40px;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.faq-item {
    background-color: var(--card-background);
    padding: 20px;
    border-radius: 10px;
}

.faq-item h3 {
    color: var(--text-color);
    margin-bottom: 10px;
}

.faq-item p {
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
    }
    
    .social-icons {
        flex-wrap: wrap;
    }
    
    .faq-grid {
        grid-template-columns: 1fr;
    }
}
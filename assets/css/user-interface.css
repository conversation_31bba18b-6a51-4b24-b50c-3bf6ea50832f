:root[data-theme="light"] {
    --card-bg: #ffffff;
    --card-border: #e1e1e1;
    --card-hover: #f5f5f5;
    --section-bg: #f8f8f8;
    --icon-bg: #f0f0f0;
    --icon-color: #555555;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:root[data-theme="dark"] {
    --card-bg: #252541;
    --card-border: #333355;
    --card-hover: #2A2A4A;
    --section-bg: #1E1E32;
    --icon-bg: #2A2A4A;
    --icon-color: #cccccc;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    --hover-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Common Card Styles */
.card {
    background: var(--card-bg);
    border-radius: 15px;
    border: 1px solid var(--card-border);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow);
}

/* Section Headers */
.section-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--section-bg);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-title {
    font-size: 1.5rem;
    color: var(--text-color);
    font-weight: 600;
}

/* Icon Styles */
.icon-container {
    width: 40px;
    height: 40px;
    background: var(--icon-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--icon-color);
    transition: var(--transition);
}

.icon-container:hover {
    transform: scale(1.1);
    background: var(--primary-color);
    color: white;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Form Controls */
.form-control {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(124, 101, 179, 0.2);
    outline: none;
}

/* Responsive Grid */
.grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Media Queries */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .grid {
        grid-template-columns: 1fr;
    }
}
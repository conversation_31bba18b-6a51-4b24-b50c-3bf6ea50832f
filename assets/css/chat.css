:root[data-theme="light"] {
    --chat-card-bg: #ffffff;
    --chat-border: #e1e1e1;
    --chat-input-bg: #f8f8f8;
    --message-sent: #7C65B3;
    --message-received: #f0f0f0;
    --online-status: #4CAF50;
}

:root[data-theme="dark"] {
    --chat-card-bg: #252541;
    --chat-border: #333355;
    --chat-input-bg: #1E1E32;
    --message-sent: #9D84D2;
    --message-received: #2A2A4A;
    --online-status: #65B3A0;
}

.chat-container {
    display: flex;
    height: 100vh;
    background-color: #f5f5f5;
}

.chat-sidebar {
    width: 300px;
    background-color: #fff;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
}

.chat-search {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.chat-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
}

.chat-search input:focus {
    border-color: #2196f3;
}

.chat-list {
    flex: 1;
    overflow-y: auto;
}

.chat-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.chat-item:hover {
    background-color: #f5f5f5;
}

.chat-item.active {
    background-color: #e3f2fd;
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    overflow: hidden;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.chat-info {
    flex: 1;
    min-width: 0;
}

.chat-name {
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-preview {
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 15px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
}

.chat-title {
    font-weight: 500;
    font-size: 16px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.message {
    max-width: 70%;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.message.sent {
    align-self: flex-end;
}

.message.received {
    align-self: flex-start;
}

.message-content {
    padding: 10px 15px;
    border-radius: 15px;
    position: relative;
}

.message.sent .message-content {
    background-color: #2196f3;
    color: #fff;
    border-bottom-right-radius: 5px;
}

.message.received .message-content {
    background-color: #fff;
    color: #333;
    border-bottom-left-radius: 5px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-content p {
    margin: 0;
    line-height: 1.4;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
    display: block;
}

.message.sent .message-time {
    color: rgba(255, 255, 255, 0.8);
}

.message.received .message-time {
    color: #666;
}

.message-status {
    font-size: 12px;
    margin-left: 5px;
    color: rgba(255, 255, 255, 0.8);
}

.typing-indicator {
    padding: 10px 15px;
    font-size: 13px;
    color: #666;
    display: none;
}

.chat-input {
    padding: 15px;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
}

.message-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    margin-right: 10px;
    font-size: 14px;
    outline: none;
    resize: none;
    max-height: 100px;
    min-height: 40px;
}

.message-input:focus {
    border-color: #2196f3;
}

.send-message {
    background-color: #2196f3;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.send-message:hover {
    background-color: #1976d2;
}

.send-message:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.unread-badge {
    background-color: #2196f3;
    color: #fff;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    display: none;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* Loading Animation */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #666;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2196f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.error {
    color: #f44336;
    text-align: center;
    padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-sidebar {
        width: 100%;
        position: absolute;
        height: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .chat-sidebar.active {
        transform: translateX(0);
    }

    .message {
        max-width: 85%;
    }
}
/* Pricing Section Styles */
.pricing-section {
    margin-bottom: 60px;
}

.pricing-section h2 {
    text-align: center;
    margin-bottom: 40px;
    color: var(--text-color);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin: 0 auto;
    max-width: 1200px;
}

.pricing-card {
    background-color: var(--card-background);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    position: relative;
    transition: transform 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.pricing-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.featured-label {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-color);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9em;
}

.pricing-card h3 {
    color: var(--text-color);
    margin-bottom: 20px;
    font-size: 1.5em;
}

.price {
    font-size: 2.5em;
    color: var(--primary-color);
    margin-bottom: 5px;
    font-weight: bold;
}

.period {
    color: var(--text-secondary);
    margin-bottom: 20px;
    font-size: 0.9em;
}

.pricing-card ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
}

.pricing-card ul li {
    margin: 10px 0;
    color: var(--text-secondary);
    padding-left: 25px;
    position: relative;
}

.pricing-card ul li::before {
    content: "✓";
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

.btn-pricing {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: background-color 0.3s ease;
    width: 80%;
    margin-top: 20px;
}

.btn-pricing:hover {
    background-color: var(--button-hover);
}

@media (max-width: 768px) {
    .pricing-card.featured {
        transform: none;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
        padding: 0 20px;
    }
}
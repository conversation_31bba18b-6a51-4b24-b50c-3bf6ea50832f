:root[data-theme="light"] {
    --bg-color: #ffffff;
    --bg-secondary: #f9fafb;
    --text-color: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --primary-color: #3b82f6;
    --primary-color-dark: #2563eb;
    --primary-color-light: rgba(59, 130, 246, 0.1);
    --hover-color: #f3f4f6;
    --input-bg: #ffffff;
    --error-color: #ef4444;
    --error-bg: #fee2e2;
    --success-color: #22c55e;
    --bg-gradient: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

:root[data-theme="dark"] {
    --bg-color: #1f2937;
    --bg-secondary: #111827;
    --text-color: #f9fafb;
    --text-secondary: #9ca3af;
    --border-color: #374151;
    --primary-color: #3b82f6;
    --primary-color-dark: #60a5fa;
    --primary-color-light: rgba(59, 130, 246, 0.2);
    --hover-color: #2d3748;
    --input-bg: #111827;
    --error-color: #f87171;
    --error-bg: #7f1d1d;
    --success-color: #34d399;
    --bg-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

body {
    margin: 0;
    font-family: 'Poppins', sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-color);
    line-height: 1.5;
}

/* Admin Layout */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    background: var(--bg-secondary);
    min-height: 100vh;
}

/* Common Components */
.card {
    background: var(--bg-color);
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.section-title {
    color: var(--text-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--input-bg);
    color: var(--text-color);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color-light);
    outline: none;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
}

.btn-primary:hover {
    background: var(--primary-color-dark);
}

.btn-secondary {
    background: var(--bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--hover-color);
}

/* Tables */
.table-container {
    overflow-x: auto;
    margin: 1.5rem 0;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-color);
    border-radius: 12px;
    overflow: hidden;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--primary-color);
    color: white;
    font-weight: 500;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tr:hover {
    background: var(--hover-color);
}

/* Status Badges */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-error {
    background: var(--error-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }

    .admin-layout {
        flex-direction: column;
    }

    .card {
        padding: 1rem;
    }

    .section-title {
        font-size: 1.25rem;
    }
}

@media (max-width: 640px) {
    .btn {
        width: 100%;
        justify-content: center;
    }

    .filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}
/* Services page specific styles */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../images/services-hero.jpg');
    background-size: cover;
    background-position: center;
    padding: 100px 0;
    margin-bottom: 50px;
    text-align: center;
}

.hero-section h1 {
    color: var(--hero-text);
    font-size: 3em;
    margin-bottom: 20px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.service-card {
    background-color: var(--card-background);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    background-color: var(--card-hover);
}

.service-icon {
    font-size: 3em;
    margin-bottom: 20px;
}

.service-card h2 {
    color: var(--text-color);
    margin-bottom: 15px;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
}

.service-features li {
    margin: 10px 0;
    color: var(--text-secondary);
    padding-left: 20px;
    position: relative;
}

.service-features li::before {
    content: "•";
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

.btn-service {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--button-primary);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: background-color 0.3s ease;
}

.btn-service:hover {
    background-color: var(--button-hover);
}

/* Pricing Section */
.pricing-section {
    margin-bottom: 60px;
}

.pricing-section h2 {
    text-align: center;
    margin-bottom: 40px;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.pricing-card {
    background-color: var(--card-background);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    position: relative;
}

.pricing-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.featured-label {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-color);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9em;
}

.pricing-card h3 {
    color: var(--text-color);
    margin-bottom: 20px;
}

.price {
    font-size: 2em;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.pricing-card ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
}

.pricing-card ul li {
    margin: 10px 0;
    color: var(--text-secondary);
}

.btn-pricing {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: background-color 0.3s ease;
    width: 80%;
}

.btn-pricing:hover {
    background-color: var(--primary-color-dark);
}

@media (max-width: 768px) {
    .pricing-card.featured {
        transform: none;
    }
    
    .services-grid,
    .pricing-grid {
        grid-template-columns: 1fr;
    }
}

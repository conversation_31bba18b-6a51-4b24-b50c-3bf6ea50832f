<?php
if (session_status() === PHP_SESSION_NONE) {
    session_name('admin_session');
    session_start();
}

function checkAdminAuth() {
    // Get the current script name
    $currentScript = basename($_SERVER['SCRIPT_NAME']);
    
    // Skip auth check for login page to prevent redirect loop
    if ($currentScript === 'login.php') {
        return;
    }
    
    // Check if admin is logged in with proper session variables
    if (!isset($_SESSION['admin_id']) || 
        !isset($_SESSION['admin_role']) || 
        $_SESSION['admin_role'] !== 'admin' || 
        !isset($_SESSION['admin_is_authenticated']) || 
        !$_SESSION['admin_is_authenticated']) {
        
        // Clear any existing admin session data
        unset($_SESSION['admin_id']);
        unset($_SESSION['admin_role']);
        unset($_SESSION['admin_is_authenticated']);
        
        // Only store redirect URL if not already on login page
        if ($currentScript !== 'login.php') {
            $_SESSION['admin_redirect_url'] = $_SERVER['REQUEST_URI'];
        }
        
        // Redirect to admin login page with relative path
        header('Location: login.php');
        exit();
    }
    
    // Update last activity timestamp
    $_SESSION['admin_last_activity'] = time();
}
// Call the auth check function
checkAdminAuth();

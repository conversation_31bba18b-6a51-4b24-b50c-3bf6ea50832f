<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
require_once '../auth/EmailHandler.php';

// Check if user is admin
session_start();
if (!isset($_SESSION['admin_role']) || $_SESSION['admin_role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['email']) && !empty($_POST['email'])) {
        $email = trim($_POST['email']);
        
        try {
            // Find user with this email
            $stmt = $pdo->prepare("SELECT id, email, role, email_verified FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                if ($user['email_verified'] == 1) {
                    $error = "This user's email is already verified.";
                } else {
                    // Update user as verified
                    $updateStmt = $pdo->prepare("UPDATE users SET email_verified = 1, verification_token = NULL WHERE id = ?");
                    if ($updateStmt->execute([$user['id']])) {
                        $message = "User email {$email} has been manually verified successfully.";
                        
                        // Send welcome email
                        try {
                            $emailHandler = new EmailHandler();
                            $emailHandler->sendWelcomeEmail($user['email'], $user['role']);
                            $message .= " Welcome email sent.";
                        } catch (Exception $e) {
                            error_log('Welcome email error: ' . $e->getMessage());
                            $error = "User verified but couldn't send welcome email: " . $e->getMessage();
                        }
                    } else {
                        $error = "Failed to update user verification status.";
                    }
                }
            } else {
                $error = "No user found with email: {$email}";
            }
        } catch (PDOException $e) {
            error_log('Manual verification error: ' . $e->getMessage());
            $error = "Database error: " . $e->getMessage();
        }
    } else {
        $error = "Email is required.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual User Verification - MindCare Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h2 class="mb-0">Manual User Verification</h2>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($message)): ?>
                            <div class="alert alert-success">
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                            <div class="mb-3">
                                <label for="email" class="form-label">User Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="form-text">Enter the email address of the user to manually verify.</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Verify User</button>
                            <a href="index.php" class="btn btn-secondary">Back to Dashboard</a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 
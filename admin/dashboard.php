<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
require_once 'auth_middleware.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Fetch dashboard statistics
$stats = [
    'total_users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'total_therapists' => $pdo->query("SELECT COUNT(*) FROM therapist_profiles")->fetchColumn(),
    'total_appointments' => $pdo->query("SELECT COUNT(*) FROM appointments")->fetchColumn(),
    'total_payments' => $pdo->query("SELECT COALESCE(SUM(amount), 0) FROM payments")->fetchColumn()
];

// Fetch recent users
$recent_users = $pdo->query("SELECT id, CONCAT(first_name, ' ', last_name) AS name, created_at FROM users ORDER BY created_at DESC LIMIT 5")->fetchAll();

// Fetch recent appointments
$recent_appointments = $pdo->query("
    SELECT a.*, 
        CONCAT(u.first_name, ' ', u.last_name) as user_name, 
        CONCAT(t.first_name, ' ', t.last_name) as therapist_name 
    FROM appointments a 
    JOIN users u ON a.user_id = u.id 
    JOIN users t ON a.therapist_id = t.id 
    LEFT JOIN therapist_profiles tp ON t.id = tp.user_id 
    ORDER BY a.appointment_date DESC LIMIT 5
")->fetchAll();
?>

<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - MindCare</title>
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
<button class="theme-toggle" aria-label="Toggle theme" style="position: fixed; top: 20px; right: 20px; z-index: 1000; width: 21px; height: 21px; border-radius: 50%; font-size: 24px; padding: 0; display: flex; align-items: center; justify-content: center;">🌙</button>

    <div class="dashboard-layout">
        <?php include 'includes/sidebar.php'; ?>
        <main class="main-content">
            <div class="content-header">
                <h1>Dashboard Overview</h1>
                <div class="admin-actions">
                    <button class="bulk-btn" onclick="window.location.href='users.php'">
                        <i class="fas fa-users"></i> Manage Users
                    </button>
                    <button class="bulk-btn" onclick="window.location.href='settings.php'">
                        <i class="fas fa-cog"></i> System Settings
                    </button>
                </div>
            </div>

        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-users icon"></i>
                <h3>Total Users</h3>
                <div class="value"><?php echo number_format($stats['total_users']); ?></div>
            </div>
            <div class="stat-card">
                <i class="fas fa-user-md icon"></i>
                <h3>Total Therapists</h3>
                <div class="value"><?php echo number_format($stats['total_therapists']); ?></div>
            </div>
            <div class="stat-card">
                <i class="fas fa-calendar-check icon"></i>
                <h3>Total Appointments</h3>
                <div class="value"><?php echo number_format($stats['total_appointments']); ?></div>
            </div>
            <div class="stat-card">
                <i class="fas fa-dollar-sign icon"></i>
                <h3>Total Revenue</h3>
                <div class="value">$<?php echo number_format($stats['total_payments'], 2); ?></div>
            </div>
        </div>

        <div class="recent-activity">
            <div class="activity-card">
                <h2><i class="fas fa-user-plus"></i> Recent Users</h2>
                <ul class="activity-list">
                    <?php foreach ($recent_users as $user): ?>
                    <li class="activity-item">
                        <div class="name"><?php echo htmlspecialchars($user['name']); ?></div>
                        <div class="date">Joined: <?php echo date('M d, Y', strtotime($user['created_at'])); ?></div>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <div class="activity-card">
                <h2><i class="fas fa-calendar-alt"></i> Recent Appointments</h2>
                <ul class="activity-list">
                    <?php foreach ($recent_appointments as $appointment): ?>
                    <li class="activity-item">
                        <div class="name">
                            <?php echo htmlspecialchars($appointment['user_name']); ?> with 
                            <?php echo htmlspecialchars($appointment['therapist_name']); ?>
                        </div>
                        <div class="date">
                            <?php echo date('M d, Y h:i A', strtotime($appointment['appointment_date'])); ?>
                        </div>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>

    <script src="../assets/js/theme.js"></script>
</body>
</html>
<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
require_once 'auth_middleware.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Fetch statistics
$stats = [
    // User Statistics
    'total_users' => $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'user'")->fetchColumn(),
    'total_therapists' => $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'therapist'")->fetchColumn(),
    'pending_therapists' => $pdo->query("SELECT COUNT(*) FROM therapist_profiles WHERE status = 'pending'")->fetchColumn(),
    
    // Appointment Statistics
    'total_appointments' => $pdo->query("SELECT COUNT(*) FROM appointments")->fetchColumn(),
    'completed_appointments' => $pdo->query("SELECT COUNT(*) FROM appointments WHERE status = 'completed'")->fetchColumn(),
    
    // Revenue Statistics
    'total_revenue' => $pdo->query("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE payment_status = 'completed'")->fetchColumn(),
    'monthly_revenue' => $pdo->query("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE payment_status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)")->fetchColumn()
];

// Fetch monthly appointment trends
$monthly_trends = $pdo->query("
    SELECT 
        DATE_FORMAT(appointment_date, '%Y-%m') as month,
        COUNT(*) as total
    FROM appointments
    WHERE appointment_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
    GROUP BY DATE_FORMAT(appointment_date, '%Y-%m')
    ORDER BY month ASC
")->fetchAll(PDO::FETCH_ASSOC);

// Fetch top therapists
$top_therapists = $pdo->query("
    SELECT 
        t.username,
        COUNT(a.id) as total_sessions,
        AVG(r.rating) as avg_rating
    FROM users t
    LEFT JOIN appointments a ON t.id = a.therapist_id
    LEFT JOIN therapist_ratings r ON t.id = r.therapist_id
    WHERE t.role = 'therapist'
    GROUP BY t.id
    ORDER BY total_sessions DESC
    LIMIT 5
")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - MindCare Admin</title>
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="assets/css/appointments.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<button class="theme-toggle" aria-label="Toggle theme" style="position: fixed; top: 20px; right: 20px; z-index: 1000; width: 21px; height: 21px; border-radius: 50%; font-size: 24px; padding: 0; display: flex; align-items: center; justify-content: center;">🌙</button>

    <?php include 'includes/sidebar.php'; ?>

    <div class="admin-container">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
            <div>
                <h1 class="h2 mb-1">Reports & Analytics</h1>
                <p class="text-muted">Platform insights and performance metrics</p>
            </div>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-primary" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>Export Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Overview Stats -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Total Users</h6>
                        <h2 class="card-title mb-0"><?php echo $stats['total_users']; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Total Therapists</h6>
                        <h2 class="card-title mb-0"><?php echo $stats['total_therapists']; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Total Appointments</h6>
                        <h2 class="card-title mb-0"><?php echo $stats['total_appointments']; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Total Revenue</h6>
                        <h2 class="card-title mb-0">$<?php echo number_format($stats['total_revenue'], 2); ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Appointment Trends</h5>
                        <canvas id="appointmentTrends"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Revenue Overview</h5>
                        <canvas id="revenueOverview"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Therapists -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Top Performing Therapists</h5>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Therapist</th>
                                <th>Total Sessions</th>
                                <th>Average Rating</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_therapists as $therapist): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($therapist['username']); ?></td>
                                    <td><?php echo $therapist['total_sessions']; ?></td>
                                    <td>
                                        <?php echo number_format($therapist['avg_rating'], 1); ?>
                                        <i class="fas fa-star text-warning"></i>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/theme.js"></script>
    <script>
    // Prepare data for charts
    const monthlyData = <?php echo json_encode(array_column($monthly_trends, 'total')); ?>;
    const monthLabels = <?php echo json_encode(array_column($monthly_trends, 'month')); ?>;

    // Appointment Trends Chart
    const appointmentTrends = new Chart(
        document.getElementById('appointmentTrends'),
        {
            type: 'line',
            data: {
                labels: monthLabels,
                datasets: [{
                    label: 'Monthly Appointments',
                    data: monthlyData,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        }
    );
    </script>
</body>
</html>
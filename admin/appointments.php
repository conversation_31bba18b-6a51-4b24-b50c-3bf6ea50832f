<?php
require_once 'auth_middleware.php';
require_once '../config/config.php';
require_once '../api/notification_handler.php';

// Handle appointment status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['appointment_id'], $_POST['action'])) {
    $appointmentId = $_POST['appointment_id'];
    $action = $_POST['action'];
    
    try {
        if ($action === 'cancel') {
            $stmt = $pdo->prepare("UPDATE appointments SET status = 'cancelled' WHERE id = ?");
            $stmt->execute([$appointmentId]);
            
            // Create notifications
            $notificationHandler = new NotificationHandler($pdo);
            $notificationHandler->handleAppointmentNotification($appointmentId, 'cancelled');
            
            echo json_encode(['success' => true, 'message' => 'Appointment cancelled successfully']);
        }
        exit();
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'Error updating appointment']);
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointments Management - MindCare Admin</title>
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="assets/css/appointments.css">
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
</head>
<body>
    <button class="theme-toggle" aria-label="Toggle theme" style="position: fixed; top: 20px; right: 20px; z-index: 1000; width: 21px; height: 21px; border-radius: 50%; font-size: 24px; padding: 0; display: flex; align-items: center; justify-content: center;">🌙</button>

    <div class="dashboard-layout">
        <?php include 'includes/sidebar.php';?>
        
        <main class="main-content">
            <div class="content-header">
                <h1>Appointments Management</h1>
                <p class="text-muted">View and manage all appointments</p>
            </div>

            <!-- Filters -->
            <div class="filters">
                <div class="filter-group">
                    <input type="date" class="form-control" id="dateFilter" placeholder="Filter by date">
                </div>
                <div class="filter-group">
                    <select class="form-select" id="statusFilter">
                        <option value="">All Status</option>
                        <option value="scheduled">Scheduled</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="filter-group">
                    <input type="text" class="form-control" id="searchFilter" placeholder="Search appointments...">
                </div>
            </div>

            <!-- Appointments Table -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Date</th>
                            <th>Client</th>
                            <th>Therapist</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        // Fetch appointments with related information
                        $stmt = $pdo->prepare("SELECT 
                            a.id,
                            a.appointment_date,
                            a.status,
                            a.created_at,
                            u.username as client_name,
                            t.username as therapist_name
                        FROM appointments a
                        JOIN users u ON a.user_id = u.id
                        JOIN users t ON a.therapist_id = t.id
                        ORDER BY a.appointment_date DESC");
                        $stmt->execute();
                        $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        foreach ($appointments as $appointment): 
                        ?>
                            <tr>
                                <td><?php echo $appointment['id']; ?></td>
                                <td><?php echo date('M j, Y g:i A', strtotime($appointment['appointment_date'])); ?></td>
                                <td><?php echo htmlspecialchars($appointment['client_name']); ?></td>
                                <td><?php echo htmlspecialchars($appointment['therapist_name']); ?></td>
                                <td>
                                    <span class="status-badge <?php echo getStatusBadgeClass($appointment['status']); ?>">
                                        <?php echo ucfirst($appointment['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="action-btn view-btn" onclick="viewAppointment(<?php echo $appointment['id']; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if ($appointment['status'] === 'scheduled'): ?>
                                        <button class="action-btn cancel-btn" onclick="cancelAppointment(<?php echo $appointment['id']; ?>)">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        <?php if (empty($appointments)): ?>
                            <tr>
                                <td colspan="6" class="text-center">No appointments found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="../assets/js/theme.js"></script>

    <script>
        function getStatusBadgeClass(status) {
            switch (status) {
                case 'scheduled':
                    return 'bg-primary';
                case 'completed':
                    return 'bg-success';
                case 'cancelled':
                    return 'bg-danger';
                default:
                    return 'bg-secondary';
            }
        }

        function viewAppointment(id) {
            // Implement view appointment details
            Swal.fire({
                title: 'Appointment Details',
                text: 'Viewing appointment ID: ' + id,
                icon: 'info',
                confirmButtonText: 'Close'
            });
        }

        function cancelAppointment(id) {
            Swal.fire({
                title: 'Cancel Appointment',
                text: 'Are you sure you want to cancel this appointment?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, cancel it',
                cancelButtonText: 'No, keep it'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Send AJAX request to cancel appointment
                    $.ajax({
                        url: 'appointments.php',
                        type: 'POST',
                        data: {
                            appointment_id: id,
                            action: 'cancel'
                        },
                        success: function(response) {
                            const data = JSON.parse(response);
                            if (data.success) {
                                Swal.fire({
                                    title: 'Cancelled!',
                                    text: data.message,
                                    icon: 'success'
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: data.message,
                                    icon: 'error'
                                });
                            }
                        }
                    });
                }
            });
        }

        // Initialize DataTable
        $(document).ready(function() {
            $('.table').DataTable({
                responsive: true,
                order: [[1, 'desc']], // Sort by date column by default
                pageLength: 10,
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        });
    </script>
</body>
</html>
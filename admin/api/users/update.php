<?php
require_once '../../../config/config.php';
require_once '../../auth_middleware.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $data = json_decode(file_get_contents('php://input'), true);
    
    if ($data === null) {
        throw new Exception('Invalid JSON data');
    }

    $id = $data['id'] ?? '';
    $firstName = $data['first_name'] ?? '';
    $lastName = $data['last_name'] ?? '';
    $email = $data['email'] ?? '';
    $role = $data['role'] ?? '';
    $password = $data['password'] ?? '';
    $active = isset($data['active']) ? 1 : 0;
    
    // Validate required fields
    if (empty($id) || empty($firstName) || empty($lastName) || empty($email) || empty($role)) {
        throw new Exception('Required fields are missing');
    }
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // Check if email already exists for other users
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
    $stmt->execute([$email, $id]);
    if ($stmt->rowCount() > 0) {
        throw new Exception('Email already exists');
    }
    
    // Prepare update query
    $updateFields = ['first_name = ?', 'last_name = ?', 'email = ?', 'role = ?', 'active = ?'];
    $params = [$firstName, $lastName, $email, $role, $active];

    // Add password update if provided
    if (!empty($password)) {
        $updateFields[] = 'password = ?';
        $params[] = password_hash($password, PASSWORD_DEFAULT);
    }

    // Add id to params
    $params[] = $id;

    // Update user
    $query = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $stmt = $pdo->prepare($query);
    $result = $stmt->execute($params);
    
    if ($result) {
        echo json_encode(['success' => true, 'message' => 'User updated successfully']);
    } else {
        throw new Exception('Failed to update user');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
<?php
require_once '../../../config/config.php';
require_once '../../../config/no_cache.php';
require_once '../../auth_middleware.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['action']) || !isset($input['users']) || !is_array($input['users'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request data']);
    exit;
}

try {
    $pdo->beginTransaction();
    
    switch ($input['action']) {
        case 'activate':
            $stmt = $pdo->prepare("UPDATE users SET active = 1 WHERE id = ?");
            break;
        case 'deactivate':
            $stmt = $pdo->prepare("UPDATE users SET active = 0 WHERE id = ?");
            break;
        case 'delete':
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            exit;
    }
    
    foreach ($input['users'] as $userId) {
        $stmt->execute([$userId]);
    }
    
    $pdo->commit();
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    $pdo->rollBack();
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
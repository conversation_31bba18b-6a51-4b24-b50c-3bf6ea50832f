<?php
require_once '../../../config/config.php';
require_once '../../auth_middleware.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get POST data
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'User ID is required']);
    exit();
}

$userId = $data['id'];

try {
    $pdo->beginTransaction();

    // Check if user exists
    $checkStmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
    $checkStmt->execute([$userId]);
    if (!$checkStmt->fetch()) {
        throw new Exception('User not found');
    }

    // Delete related records first
    $tables = ['appointments', 'messages', 'payments', 'user_settings'];
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("DELETE FROM $table WHERE user_id = ?");
        $stmt->execute([$userId]);
    }

    // Delete the user
    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$userId]);

    // Log the deletion
    $log_stmt = $pdo->prepare(
        "INSERT INTO admin_logs (admin_id, action, details, ip_address) VALUES (?, 'delete_user', ?, ?)"
    );
    $log_stmt->execute([
        $_SESSION['admin_id'],
        json_encode(['user_id' => $userId]),
        $_SERVER['REMOTE_ADDR']
    ]);

    $pdo->commit();
    echo json_encode([
        'success' => true,
        'message' => 'User deleted successfully'
    ]);

} catch (Exception $e) {
    $pdo->rollBack();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error deleting user: ' . $e->getMessage()
    ]);
}
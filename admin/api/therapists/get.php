<?php
require_once '../../../config/config.php';
require_once '../../../config/no_cache.php';
require_once '../../auth_middleware.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Check if ID is provided for single therapist retrieval
if (isset($_GET['id'])) {
    if (!is_numeric($_GET['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid therapist ID']);
        exit;
    }

// Fetch therapist data with profile information
$stmt = $pdo->prepare("
    SELECT 
        tp.id as profile_id,
        tp.user_id,
        tp.status,
        tp.specialization,
        tp.experience_years,
        tp.hourly_rate,
        tp.created_at,
        tp.updated_at,
        u.id as therapist_id,
        u.username,
        u.email,
        u.first_name,
        u.last_name,
        u.phone,
        u.role,
        u.active,
        CONCAT(u.first_name, ' ', u.last_name) as full_name,
        COALESCE((SELECT COUNT(*) FROM appointments a WHERE a.therapist_id = u.id), 0) as total_sessions,
        COALESCE((SELECT AVG(rating) FROM therapist_ratings r WHERE r.therapist_id = u.id), 0) as avg_rating
    FROM therapist_profiles tp
    JOIN users u ON u.id = tp.user_id
    WHERE u.id = ?
");

try {
    $stmt->execute([$_GET['id']]);
    $therapist = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$therapist) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Therapist not found']);
        exit;
    }
    
    // Format numeric values
    $therapist['hourly_rate'] = number_format(floatval($therapist['hourly_rate']), 2);
    $therapist['avg_rating'] = number_format(floatval($therapist['avg_rating']), 1);
    $therapist['experience_years'] = intval($therapist['experience_years']);
    $therapist['total_sessions'] = intval($therapist['total_sessions']);
    
    echo json_encode(['success' => true, 'therapist' => $therapist]);
} catch (PDOException $e) {
    error_log('Database error in therapist get.php: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
    exit;
}
} else {
    // List all therapists with pagination
    try {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(50, intval($_GET['limit']))) : 10;
        $offset = ($page - 1) * $limit;
        $status = isset($_GET['status']) ? $_GET['status'] : null;
        
        $where = '';
        $params = [];
        
        if ($status) {
            $where = 'WHERE tp.status = ?';
            $params[] = $status;
        }
        
        // Get total count
        $countStmt = $pdo->prepare("SELECT COUNT(*) FROM therapist_profiles tp $where");
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();
        
        // Get therapists
        $stmt = $pdo->prepare("
            SELECT 
                tp.id as profile_id,
                tp.user_id,
                tp.status,
                tp.specialization,
                tp.experience_years,
                tp.hourly_rate,
                tp.created_at,
                u.id as therapist_id,
                u.username,
                u.email,
                u.first_name,
                u.last_name,
                u.phone,
                CONCAT(u.first_name, ' ', u.last_name) as full_name
            FROM therapist_profiles tp
            JOIN users u ON u.id = tp.user_id
            $where
            ORDER BY tp.created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $therapists = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $totalPages = ceil($total / $limit);
        
        echo json_encode([
            'success' => true,
            'therapists' => $therapists,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_records' => $total,
                'limit' => $limit
            ]
        ]);
        
    } catch (PDOException $e) {
        error_log('Database error in therapist get.php: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error occurred']);
        exit;
    }
}
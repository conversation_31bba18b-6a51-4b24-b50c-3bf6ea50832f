<?php
require_once '../../../config/config.php';
require_once '../../../config/no_cache.php';
require_once '../../auth_middleware.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Parse JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
    exit;
}

try {
    // Validate required fields
    if (!isset($input['id']) || !is_numeric($input['id'])) {
        throw new Exception('Invalid or missing therapist profile ID');
    }
    
    if (!isset($input['status'])) {
        throw new Exception('Status is required');
    }

    // Start transaction
    $pdo->beginTransaction();

    // First check if the therapist profile exists
    $checkStmt = $pdo->prepare("SELECT tp.*, u.id as user_id FROM therapist_profiles tp JOIN users u ON u.id = tp.user_id WHERE tp.id = ?");
    $checkStmt->execute([$input['id']]);
    $therapist = $checkStmt->fetch(PDO::FETCH_ASSOC);

    if (!$therapist) {
        throw new Exception('Therapist profile not found');
    }

    // Update therapist profile
    $stmt = $pdo->prepare("UPDATE therapist_profiles 
                          SET status = :status,
                              specialization = COALESCE(:specialization, specialization),
                              experience_years = COALESCE(:experience_years, experience_years),
                              hourly_rate = COALESCE(:hourly_rate, hourly_rate),
                              updated_at = NOW()
                          WHERE id = :id");

    $updateParams = [
        ':id' => $input['id'],
        ':status' => $input['status'],
        ':specialization' => $input['specialization'] ?? null,
        ':experience_years' => isset($input['experience_years']) ? intval($input['experience_years']) : null,
        ':hourly_rate' => isset($input['hourly_rate']) ? floatval($input['hourly_rate']) : null
    ];

    $stmt->execute($updateParams);

    // If user data is provided, update user table
    if (isset($input['user_data']) && is_array($input['user_data'])) {
        $updates = [];
        $params = [':id' => $therapist['user_id']];

        $allowedFields = ['email', 'first_name', 'last_name', 'phone'];
        foreach ($allowedFields as $field) {
            if (isset($input['user_data'][$field])) {
                $updates[] = "$field = :$field";
                $params[":$field"] = $input['user_data'][$field];
            }
        }

        if (!empty($updates)) {
            $sql = "UPDATE users SET " . implode(", ", $updates) . ", updated_at = NOW() WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
        }
    }

    // If status is being changed to approved, update user role
    if ($input['status'] === 'approved' && $therapist['status'] !== 'approved') {
        $stmt = $pdo->prepare("UPDATE users SET role = 'therapist', active = 1, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$therapist['user_id']]);
    }

    $pdo->commit();

    // Fetch updated therapist data
    $stmt = $pdo->prepare("
        SELECT 
            tp.id as profile_id,
            tp.user_id,
            tp.status,
            tp.specialization,
            tp.experience_years,
            tp.hourly_rate,
            tp.updated_at,
            u.email,
            u.first_name,
            u.last_name,
            u.phone,
            CONCAT(u.first_name, ' ', u.last_name) as full_name
        FROM therapist_profiles tp
        JOIN users u ON u.id = tp.user_id
        WHERE tp.id = ?
    ");
    $stmt->execute([$input['id']]);
    $updatedTherapist = $stmt->fetch(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true, 
        'message' => 'Therapist updated successfully',
        'therapist' => $updatedTherapist
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log('Error in therapist update.php: ' . $e->getMessage());
    http_response_code($e->getMessage() === 'Therapist profile not found' ? 404 : 500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    exit;
}
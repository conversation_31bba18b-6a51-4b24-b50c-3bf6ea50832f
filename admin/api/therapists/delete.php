<?php
require_once '../../../config/config.php';
require_once '../../auth_middleware.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    if (!isset($_POST['id'])) {
        throw new Exception('Therapist ID is required');
    }

    $therapist_id = $_POST['id'];

    // Start transaction
    $pdo->beginTransaction();

    // Check if therapist has any active appointments
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM appointments WHERE therapist_id = ? AND appointment_date >= CURDATE()");
    $stmt->execute([$therapist_id]);
    $active_appointments = $stmt->fetchColumn();

    if ($active_appointments > 0) {
        throw new Exception('Cannot delete therapist with active appointments');
    }

    // Delete from therapist_profiles
    $stmt = $pdo->prepare("DELETE FROM therapist_profiles WHERE therapist_id = ?");
    $stmt->execute([$therapist_id]);

    // Update user role to 'user' instead of deleting the account
    $stmt = $pdo->prepare("UPDATE users SET role = 'user', updated_at = NOW() WHERE id = ?");
    $stmt->execute([$therapist_id]);

    $pdo->commit();
    echo json_encode(['success' => true, 'message' => 'Therapist removed successfully']);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
<?php
require_once '../../../config/config.php';
require_once '../../auth_middleware.php';

header('Content-Type: application/json');

if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid appointment ID']);
    exit;
}

try {
    // Start transaction
    $pdo->beginTransaction();

    // Get appointment details first
    $stmt = $pdo->prepare("SELECT status FROM appointments WHERE id = ?");
    $stmt->execute([$_POST['id']]);
    $appointment = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$appointment) {
        throw new Exception('Appointment not found');
    }

    if ($appointment['status'] === 'cancelled') {
        throw new Exception('Appointment is already cancelled');
    }

    // Update appointment status
    $stmt = $pdo->prepare("UPDATE appointments SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP WHERE id = ?");
    $stmt->execute([$_POST['id']]);

    // Create notification for both user and therapist
    $stmt = $pdo->prepare("INSERT INTO notifications (user_id, reference_id, type, title, message)
        SELECT user_id, id, 'appointment', 'Appointment Cancelled',
        'Your appointment has been cancelled by the administrator.'
        FROM appointments WHERE id = ?
        UNION ALL
        SELECT therapist_id, id, 'appointment', 'Appointment Cancelled',
        'An appointment has been cancelled by the administrator.'
        FROM appointments WHERE id = ?");
    $stmt->execute([$_POST['id'], $_POST['id']]);

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Appointment cancelled successfully'
    ]);
} catch (Exception $e) {
    $pdo->rollBack();
    error_log($e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
<?php
require_once '../../../config/config.php';
require_once '../../auth_middleware.php';

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid appointment ID']);
    exit;
}

try {
    $stmt = $pdo->prepare("SELECT 
        a.id,
        a.appointment_date,
        a.duration,
        a.status,
        a.notes,
        u.username as client_name,
        u.email as client_email,
        t.username as therapist_name,
        tp.specialization
    FROM appointments a
    JOIN users u ON a.user_id = u.id
    JOIN users t ON a.therapist_id = t.id
    LEFT JOIN therapist_profiles tp ON t.id = tp.therapist_id
    WHERE a.id = ?");

    $stmt->execute([$_GET['id']]);
    $appointment = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($appointment) {
        // Format the appointment date
        $appointment['appointment_date'] = date('F j, Y g:i A', strtotime($appointment['appointment_date']));
        
        echo json_encode([
            'success' => true,
            'data' => $appointment
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Appointment not found'
        ]);
    }
} catch (PDOException $e) {
    error_log($e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
<?php
require_once '../../../config/config.php';
require_once '../../auth_middleware.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    $pdo->beginTransaction();

    // Update general settings
    $generalSettings = [
        'site_name' => $_POST['site_name'] ?? null,
        'site_description' => $_POST['site_description'] ?? null,
        'contact_email' => $_POST['contact_email'] ?? null
    ];

    // Update security settings
    $securitySettings = [
        'max_login_attempts' => (int)($_POST['max_login_attempts'] ?? 5),
        'session_timeout' => (int)($_POST['session_timeout'] ?? 30),
        'two_factor_auth' => isset($_POST['two_factor_auth']) ? (bool)$_POST['two_factor_auth'] : false
    ];

    // Update maintenance settings
    $maintenanceSettings = [
        'maintenance_mode' => isset($_POST['maintenance_mode']) ? (bool)$_POST['maintenance_mode'] : false,
        'maintenance_message' => $_POST['maintenance_message'] ?? 'Site is under maintenance'
    ];

    // Update email settings
    $emailSettings = [
        'smtp_host' => $_POST['smtp_host'] ?? null,
        'smtp_port' => (int)($_POST['smtp_port'] ?? 587),
        'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls'
    ];

    // Combine all settings
    $settings = array_merge(
        $generalSettings,
        $securitySettings,
        $maintenanceSettings,
        $emailSettings
    );

    // Update settings in database
    $updateStmt = $pdo->prepare(
        "UPDATE system_settings SET "
        . implode(', ', array_map(fn($key) => "$key = ?", array_keys($settings)))
        . " WHERE id = 1"
    );
    $updateStmt->execute(array_values($settings));

    // Log the settings update
    $log_stmt = $pdo->prepare(
        "INSERT INTO admin_logs (admin_id, action, details, ip_address) VALUES (?, 'update_settings', ?, ?)"
    );
    $log_stmt->execute([
        $_SESSION['admin_id'],
        json_encode(['updated_fields' => array_keys($settings)]),
        $_SERVER['REMOTE_ADDR']
    ]);

    $pdo->commit();
    echo json_encode([
        'success' => true,
        'message' => 'Settings updated successfully'
    ]);

} catch (Exception $e) {
    $pdo->rollBack();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error updating settings: ' . $e->getMessage()
    ]);
}
.main-content {
    padding: 2rem;
    margin-left: 300px;
    transition: margin-left 0.3s ease;
}

.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 2px 4px var(--shadow-color);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px var(--shadow-color);
}

.card-body {
    padding: 1.5rem;
}

.table {
    color: var(--text-primary);
}

.table th {
    background-color: var(--table-header-bg);
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.table td {
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table tr:hover {
    background-color: var(--table-hover);
}

.profile-image {
    object-fit: cover;
    border: 2px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-color);
}

.rating-stars {
    color: var(--warning-color);
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
}

.btn-group .btn {
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px var(--shadow-color);
}

.form-select,
.form-control {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-select:focus,
.form-control:focus {
    background-color: var(--bg-primary);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.25rem rgba(124, 101, 179, 0.25);
}

.modal-content {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_filter input {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 6px;
    padding: 0.5rem;
}

.dataTables_wrapper .dataTables_length select {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 6px;
    padding: 0.5rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    color: var(--text-primary) !important;
    background-color: var(--bg-primary) !important;
    border: 1px solid var(--border-color) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px var(--shadow-color);
}

@media (max-width: 1024px) {
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
} 
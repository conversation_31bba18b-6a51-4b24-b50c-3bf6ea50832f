/* Payments Page Styles */
:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --border-color: #e3e6f0;
    --card-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.35rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
}

.card-body {
    padding: 1.25rem;
}

.card-title {
    color: var(--dark-color);
    font-weight: 700;
    margin-bottom: 0.75rem;
}

.card-subtitle {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid var(--border-color);
    color: var(--dark-color);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.table tbody td {
    vertical-align: middle;
    color: var(--dark-color);
    font-size: 0.875rem;
}

/* Badge Styles */
.badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 600;
    border-radius: 0.25rem;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

/* Button Styles */
.btn {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.35rem;
    transition: all 0.2s ease-in-out;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    background-color: #2c9faf;
    border-color: #2a96a5;
    color: white;
}

/* Form Styles */
.form-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    font-size: 0.875rem;
    border-radius: 0.35rem;
    border: 1px solid var(--border-color);
    padding: 0.375rem 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: none;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        margin: 0 -1rem;
    }

    .btn-toolbar {
        margin-top: 1rem;
    }
}

/* Dark Theme Styles */
[data-theme="dark"] {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #2d3748;
    --dark-color: #e2e8f0;
    --border-color: #4a5568;
}

[data-theme="dark"] .card {
    background-color: #1a202c;
}

[data-theme="dark"] .table thead th {
    background-color: #2d3748;
    color: #e2e8f0;
}

[data-theme="dark"] .table tbody td {
    color: #e2e8f0;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: #2d3748;
    border-color: #4e73df;
    color: #e2e8f0;
}

/* Payments specific styles */
.dashboard-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-secondary);
    position: relative;
    width: 100%;
}

.main-content {
    flex: 1;
    padding: 2rem;
    margin-left: 250px;
    transition: margin-left 0.3s ease;
    width: calc(100% - 250px);
    min-height: 100vh;
    position: relative;
    box-sizing: border-box;
    overflow-x: hidden;
}

.content-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 2px 4px var(--shadow-color);
    width: 100%;
    box-sizing: border-box;
}

.content-header h1 {
    font-size: 1.8rem;
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

.content-header p {
    color: var(--text-secondary);
    margin: 0.5rem 0 0;
    font-size: 0.95rem;
}

/* Revenue Overview Cards */
.revenue-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.revenue-card {
    background-color: var(--bg-primary);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.revenue-card h3 {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
}

.revenue-card .amount {
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
}

.revenue-card .change {
    color: var(--success-color);
    font-size: 0.9rem;
    margin: 0.5rem 0 0 0;
}

.revenue-card .change.negative {
    color: var(--danger-color);
}

/* Filters Section */
.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    background-color: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(124, 101, 179, 0.1);
}

/* Table Styles */
.table-container {
    background-color: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 4px 6px var(--shadow-color);
    overflow: hidden;
    margin-bottom: 2rem;
    padding: 1rem;
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
}

.table th {
    background-color: var(--table-header-bg);
    padding: 1rem;
    font-weight: 600;
    text-align: left;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    vertical-align: middle;
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:hover {
    background-color: var(--table-hover);
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-block;
    text-align: center;
    min-width: 100px;
    text-transform: capitalize;
}

.status-badge.completed {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-badge.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.status-badge.failed {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* Action Buttons */
.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    color: var(--primary-color);
    background-color: rgba(124, 101, 179, 0.1);
}

.view-btn:hover {
    color: var(--primary-color);
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1000;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.theme-toggle:hover {
    transform: scale(1.1);
    background-color: var(--bg-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        margin-left: 0;
        width: 100%;
        padding: 1rem;
    }

    .filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .revenue-overview {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .content-header {
        padding: 1rem;
    }

    .content-header h1 {
        font-size: 1.5rem;
    }

    .table-container {
        padding: 0.5rem;
    }

    .table th,
    .table td {
        padding: 0.75rem;
    }

    .status-badge {
        min-width: 80px;
        padding: 0.4rem 0.8rem;
    }
}
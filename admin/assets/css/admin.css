:root {
    --primary-color: #4a90e2;
    --success-color: #2ecc71;
    --warning-color: #f1c40f;
    --danger-color: #e74c3c;
    --text-color: #2c3e50;
    --text-secondary: #7f8c8d;
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --border-color: #e9ecef;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --card-bg: var(--bg-primary);
}

.admin-container {
    padding: 2rem;
    margin-left: 350px;
    min-height: 100vh;
    background: var(--bg-secondary);
}

.page-title {
    font-size: 1.8rem;
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

.card {
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

.card-header {
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-primary);
}

.card-body {
    padding: 1.25rem;
}

.card-footer {
    padding: 1rem 1.25rem;
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    padding: 1.5rem;
}

.stat-card h3 {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-card .value {
    color: var(--text-color);
    font-size: 1.8rem;
    font-weight: 600;
}

.stat-card .icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.table-responsive {
    margin-bottom: 1.5rem;
}

.table {
    width: 100%;
    background: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background: var(--bg-secondary);
    color: var(--text-color);
    font-weight: 600;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px var(--shadow-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

@media (max-width: 768px) {
    .admin-container {
        margin-left: 0;
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .card-footer .d-flex {
        flex-direction: column;
    }
    
    .card-footer .btn {
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }
}
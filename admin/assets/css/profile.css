/* Admin Profile Styles */
.dashboard-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-secondary);
    position: relative;
    width: 100%;
}

.main-content {
    flex: 1;
    padding: 2rem;
    margin-left: 250px;
    transition: margin-left 0.3s ease;
    width: calc(100% - 250px);
    min-height: 100vh;
    position: relative;
    box-sizing: border-box;
    overflow-x: hidden;
}

.profile-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background-color: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 4px 6px var(--shadow-color);
}

.profile-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    box-shadow: 0 4px 8px var(--shadow-color);
    transition: transform 0.3s ease;
}

.profile-avatar:hover {
    transform: scale(1.05);
}

.profile-name {
    font-size: 1.8rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.profile-role {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
}

.profile-section {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.section-title {
    font-size: 1.3rem;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
}

.section-title i {
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(124, 101, 179, 0.1);
}

.btn-submit {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-submit:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px var(--shadow-color);
}

.alert {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
}

.alert i {
    font-size: 1.2rem;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.alert-error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1000;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.theme-toggle:hover {
    transform: scale(1.1);
    background-color: var(--bg-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        margin-left: 0;
        width: 100%;
        padding: 1rem;
    }

    .profile-container {
        padding: 1.5rem;
    }

    .profile-section {
        padding: 1.5rem;
    }

    .theme-toggle {
        top: 1rem;
        right: 1rem;
    }
}

@media (max-width: 768px) {
    .profile-container {
        padding: 1rem;
    }

    .profile-section {
        padding: 1rem;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }

    .profile-name {
        font-size: 1.5rem;
    }
} 
/* Appointments specific styles */
.dashboard-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-secondary);
    position: relative;
    width: 100%;
}

.main-content {
    flex: 1;
    padding: 2rem;
    margin-left: 250px;
    transition: margin-left 0.3s ease;
    width: calc(100% - 250px);
    min-height: 100vh;
    position: relative;
    box-sizing: border-box;
    overflow-x: hidden;
}

.content-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 2px 4px var(--shadow-color);
    width: 100%;
    box-sizing: border-box;
}

.content-header h1 {
    font-size: 1.8rem;
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

.content-header p {
    color: var(--text-secondary);
    margin: 0.5rem 0 0;
    font-size: 0.95rem;
}

/* Filters Section */
.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    background-color: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(124, 101, 179, 0.1);
}

/* Table Styles */
.table-container {
    background-color: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 4px 6px var(--shadow-color);
    overflow: hidden;
    margin-bottom: 2rem;
    padding: 1rem;
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
}

.table th {
    background-color: var(--table-header-bg);
    padding: 1rem;
    font-weight: 600;
    text-align: left;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    vertical-align: middle;
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:hover {
    background-color: var(--table-hover);
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-block;
    text-align: center;
    min-width: 100px;
    text-transform: capitalize;
}

/* Action Buttons */
.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    color: var(--primary-color);
    background-color: rgba(124, 101, 179, 0.1);
}

.view-btn:hover {
    color: var(--primary-color);
}

.cancel-btn:hover {
    color: var(--danger-color);
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1000;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.theme-toggle:hover {
    transform: scale(1.1);
    background-color: var(--bg-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        margin-left: 0;
        width: 100%;
        padding: 1rem;
    }

    .filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .table-container {
        overflow-x: auto;
        padding: 0.5rem;
    }

    .table {
        min-width: 800px;
    }

    .theme-toggle {
        top: 1rem;
        right: 1rem;
    }
}

/* DataTables Custom Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_length select {
    padding: 0.375rem 2rem 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.dataTables_wrapper .dataTables_filter input {
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    margin-left: 0.5rem;
}

.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-top: 1rem;
    color: var(--text-secondary);
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.375rem 0.75rem;
    margin: 0 0.25rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-primary);
    color: var(--text-primary) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--primary-color) !important;
    border-color: var(--primary-color);
    color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--bg-secondary) !important;
    border-color: var(--border-color);
}
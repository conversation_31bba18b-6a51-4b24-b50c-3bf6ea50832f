:root[data-theme="light"] {
    --approval-tab-border: #e0e0e0;
    --approval-tab-text: #666;
    --approval-tab-active: #007bff;
    --approval-card-bg: #ffffff;
    --approval-card-shadow: rgba(0, 0, 0, 0.1);
    --approval-card-hover-shadow: rgba(0, 0, 0, 0.15);
    --approval-card-title: #333;
    --approval-card-text: #666;
    --modal-bg: #ffffff;
    --modal-text: #333;
    --modal-border: #e0e0e0;
}

:root[data-theme="dark"] {
    --approval-tab-border: #404040;
    --approval-tab-text: #b0b0b0;
    --approval-tab-active: #3498db;
    --approval-card-bg: #2c2c2c;
    --approval-card-shadow: rgba(0, 0, 0, 0.2);
    --approval-card-hover-shadow: rgba(0, 0, 0, 0.4);
    --approval-card-title: #ffffff;
    --approval-card-text: #b0b0b0;
    --modal-bg: #2c2c2c;
    --modal-text: #ffffff;
    --modal-border: #404040;
}

.content {
    padding: 2rem;
    margin-left: 350px;
    transition: margin-left 0.3s ease;
    position: relative;
    z-index: 10;
}

.approval-container {
    padding: 2rem;
    margin-left: 300px;
    transition: margin-left 0.3s ease;
}

.approval-tabs {
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 2rem;
}

.approval-tabs .nav-link {
    color: var(--text-secondary);
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.approval-tabs .nav-link:hover {
    color: var(--primary-color);
}

.approval-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    background: transparent;
}

.approval-tabs .counter {
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.applications-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

.application-card {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px var(--shadow-color);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.application-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

.application-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.application-header img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 1rem;
    object-fit: cover;
}

.application-info h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.application-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.application-details {
    margin: 1rem 0;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.detail-item strong {
    color: var(--text-primary);
}

.application-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-approve {
    background-color: var(--success-color);
    color: white;
}

.btn-reject {
    background-color: var(--danger-color);
    color: white;
}

.btn-view {
    background-color: var(--primary-color);
    color: white;
}

.document-preview-modal .modal-content {
    background-color: var(--card-bg);
    color: var(--text-primary);
}

.document-preview-modal .modal-header {
    border-bottom: 1px solid var(--border-color);
}

.document-preview-modal .modal-footer {
    border-top: 1px solid var(--border-color);
}

@media (max-width: 1024px) {
    .approval-container {
        margin-left: 0;
        padding: 1rem;
    }

    .applications-container {
        grid-template-columns: 1fr;
    }
}

/* Theme Toggle Button */
.theme-toggle {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px var(--shadow-color);
}

@media (max-width: 768px) {
    .content {
        margin-left: 0;
    }
    
    .approval-container {
        padding: 1rem;
    }
}
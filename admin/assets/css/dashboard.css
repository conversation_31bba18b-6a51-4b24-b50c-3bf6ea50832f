:root[data-theme="light"] {
    --stat-card-bg: #ffffff;
    --stat-card-shadow: rgba(0, 0, 0, 0.1);
    --stat-icon-bg: #f0f0f0;
    --stat-icon-color: #4a90e2;
    --activity-card-bg: #ffffff;
    --activity-hover: #f8f9fa;
}

:root[data-theme="dark"] {
    --stat-card-bg: #2c3e50;
    --stat-card-shadow: rgba(0, 0, 0, 0.2);
    --stat-icon-bg: #34495e;
    --stat-icon-color: #4a90e2;
    --activity-card-bg: #2c3e50;
    --activity-hover: #34495e;
}

.dashboard-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-secondary);
    position: relative;
}

.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
    margin-left: 350px;
}

.content-header {
    margin-bottom: 2rem;
}

.content-header h1 {
    font-size: 2rem;
    color: var(--text-primary);
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--stat-card-bg);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px var(--stat-card-shadow);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .icon {
    font-size: 2rem;
    color: var(--stat-icon-color);
    background: var(--stat-icon-bg);
    padding: 1rem;
    border-radius: 50%;
    margin-bottom: 1rem;
}

.stat-card h3 {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0.5rem 0;
}

.stat-card .value {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.recent-activity {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.activity-card {
    background: var(--activity-card-bg);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.activity-card h2 {
    font-size: 1.3rem;
    color: var(--text-primary);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background-color: var(--activity-hover);
}

.activity-item .name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.activity-item .date {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

@media (max-width: 1024px) {
    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .recent-activity {
        grid-template-columns: 1fr;
    }
}
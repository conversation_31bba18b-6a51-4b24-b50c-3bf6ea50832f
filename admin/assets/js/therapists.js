$(document).ready(function() {
    // Initialize DataTable
    const table = $('.table').DataTable({
        pageLength: 10,
        order: [[0, 'asc']],
        responsive: true,
        language: {
            search: "_INPUT_",
            searchPlaceholder: "Search therapists..."
        }
    });

    // Filter handlers
    $('#statusFilter, #specializationFilter, #experienceFilter').on('change', function() {
        table.draw();
    });

    $('#searchFilter').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Custom filtering function
    $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
        const status = $('#statusFilter').val();
        const specialization = $('#specializationFilter').val();
        const experience = parseInt($('#experienceFilter').val()) || 0;

        const rowStatus = $(table.row(dataIndex).node()).find('.badge').text().toLowerCase();
        const rowSpecialization = data[1];
        const rowExperience = parseInt(data[2]) || 0;

        if ((status && rowStatus !== status.toLowerCase()) ||
            (specialization && rowSpecialization !== specialization) ||
            (experience && rowExperience < experience)) {
            return false;
        }
        return true;
    });
});

function getStatusBadgeClass(status) {
    switch(status.toLowerCase()) {
        case 'active':
            return 'success';
        case 'suspended':
            return 'danger';
        case 'pending':
            return 'warning';
        default:
            return 'secondary';
    }
}

function viewProfile(therapistId) {
    $.get('api/therapists/get.php', { id: therapistId })
        .done(function(data) {
            if (!data.success) {
                showToast('Error', data.message || 'Failed to load therapist profile', 'error');
                return;
            }
            
            const therapist = data.therapist;
            const modal = $(`
                <div class="modal fade" id="viewProfileModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Therapist Profile</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-4 text-center mb-3">
                                        <img src="../../uploads/therapists/${therapist.therapist_id}/profile.jpg" 
                                             class="img-fluid rounded-circle mb-2 profile-image" 
                                             style="width: 150px; height: 150px; object-fit: cover;"
                                             onerror="this.onerror=null; this.src='assets/images/default-profile.jpg';"
                                             alt="Profile Image">
                                    </div>
                                    <div class="col-md-8">
                                        <h4>${therapist.username || therapist.full_name}</h4>
                                        <p class="text-muted">${therapist.email}</p>
                                        <div class="mt-3">
                                            <p><strong>Specialization:</strong> ${therapist.specialization}</p>
                                            <p><strong>Experience:</strong> ${therapist.experience_years} years</p>
                                            <p><strong>Hourly Rate:</strong> KSH ${therapist.hourly_rate}</p>
                                            <p><strong>Status:</strong> <span class="badge bg-${getStatusBadgeClass(therapist.status)}">${therapist.status}</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `);
            modal.modal('show');
            modal.on('hidden.bs.modal', function() {
                modal.remove();
            });
        })
        .fail(function(jqXHR) {
            showToast('Error', jqXHR.responseJSON?.error || 'Failed to load therapist profile', 'error');
        });
}

function editStatus(therapistId) {
    $('#editTherapistId').val(therapistId);
    $.get('api/therapists/get.php', { id: therapistId })
        .done(function(data) {
            if (!data.success) {
                showToast('Error', data.message || 'Failed to load therapist data', 'error');
                return;
            }
            
            const therapist = data.therapist;
            $('#editStatus').val(therapist.status);
            $('#editHourlyRate').val(therapist.hourly_rate);
            $('#editTherapistModal').modal('show');
        })
        .fail(function(jqXHR) {
            showToast('Error', jqXHR.responseJSON?.error || 'Failed to load therapist data', 'error');
        });
}

function updateStatus() {
    const therapistId = $('#editTherapistId').val();
    const status = $('#editStatus').val();
    const hourlyRate = $('#editHourlyRate').val();

    $.ajax({
        url: 'api/therapists/update.php',
        method: 'POST',
        data: {
            id: therapistId,
            status: status,
            hourly_rate: hourlyRate
        },
        success: function(response) {
            if (response.success) {
                showToast('Success', 'Therapist status updated successfully', 'success');
                $('#editTherapistModal').modal('hide');
                // Refresh the table
                $('#therapistsTable').DataTable().ajax.reload();
            } else {
                showToast('Error', response.message || 'Failed to update therapist status', 'error');
            }
        },
        error: function(jqXHR) {
            showToast('Error', jqXHR.responseJSON?.error || 'Failed to update therapist status', 'error');
        }
    });
}

function suspendTherapist(therapistId) {
    if (confirm('Are you sure you want to suspend this therapist? This action can be reversed later.')) {
        $.post('api/therapists/update.php', { 
            therapist_id: therapistId,
            status: 'suspended'
        })
        .done(function(response) {
            showToast('Success', 'Therapist suspended successfully');
            setTimeout(() => location.reload(), 1500);
        })
        .fail(function(jqXHR) {
            showToast('Error', jqXHR.responseJSON?.error || 'Failed to suspend therapist', 'error');
        });
    }
}

function exportTherapists() {
    window.location.href = 'api/therapists/export.php';
}

function showToast(title, message, type = 'success') {
    const toast = $(`
        <div class="toast" role="alert">
            <div class="toast-header bg-${type === 'success' ? 'success' : 'danger'} text-white">
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">${message}</div>
        </div>
    `);
    $('.toast-container').append(toast);
    const bsToast = new bootstrap.Toast(toast[0]);
    bsToast.show();
    toast.on('hidden.bs.toast', function() {
        toast.remove();
    });
} 
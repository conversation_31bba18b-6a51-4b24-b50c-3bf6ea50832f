document.addEventListener('DOMContentLoaded', function() {
    const approvalButtons = document.querySelectorAll('.approve-btn, .reject-btn');
    const statusTabs = document.querySelectorAll('.status-tab');
    const applicationCards = document.querySelectorAll('.application-card');
    
    // Handle approval/rejection buttons
    approvalButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();
            
            const card = this.closest('.application-card');
            const applicationId = card.dataset.applicationId;
            const action = this.classList.contains('approve-btn') ? 'approve' : 'reject';
            const actionText = action === 'approve' ? 'approve' : 'reject';
            
            if (!confirm(`Are you sure you want to ${actionText} this application?`)) {
                return;
            }
            
            try {
                // Disable buttons during processing
                const buttons = card.querySelectorAll('button');
                buttons.forEach(btn => btn.disabled = true);
                
                // Show loading state
                const statusBadge = card.querySelector('.status-badge');
                const originalStatus = statusBadge.textContent;
                statusBadge.textContent = 'Processing...';
                statusBadge.className = 'status-badge status-pending';
                
                const response = await fetch('process_therapist_approval.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `application_id=${applicationId}&action=${action}`
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || 'Failed to process application');
                }
                
                // Update UI based on response
                statusBadge.textContent = data.status;
                statusBadge.className = `status-badge status-${data.status}`;
                
                // Remove action buttons
                const actionButtons = card.querySelector('.action-buttons');
                actionButtons.innerHTML = `<span class="text-muted">${data.status === 'approved' ? 'Approved' : 'Rejected'}</span>`;
                
                // Show success message
                showNotification('success', data.message);
                
                // Move card to appropriate tab after a short delay
                setTimeout(() => {
                    const targetTab = document.querySelector(`#${data.status}-tab`);
                    if (targetTab) {
                        const targetContainer = targetTab.querySelector('.applications-container');
                        if (targetContainer) {
                            targetContainer.appendChild(card);
                            // Update tab counters
                            updateTabCounters();
                        }
                    }
                }, 1000);
                
            } catch (error) {
                console.error('Error:', error);
                showNotification('error', error.message);
                
                // Reset button states
                const buttons = card.querySelectorAll('button');
                buttons.forEach(btn => btn.disabled = false);
                
                // Reset status badge
                const statusBadge = card.querySelector('.status-badge');
                statusBadge.textContent = originalStatus;
                statusBadge.className = `status-badge status-${originalStatus.toLowerCase()}`;
            }
        });
    });
    
    // Handle tab switching
    statusTabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active tab
            statusTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // Show/hide appropriate cards
            const targetStatus = this.id.replace('-tab', '');
            applicationCards.forEach(card => {
                const cardStatus = card.querySelector('.status-badge').textContent.toLowerCase();
                card.style.display = cardStatus === targetStatus ? 'block' : 'none';
            });
        });
    });
    
    // Update tab counters
    function updateTabCounters() {
        const counters = {
            pending: document.querySelectorAll('.application-card .status-badge.status-pending').length,
            approved: document.querySelectorAll('.application-card .status-badge.status-approved').length,
            rejected: document.querySelectorAll('.application-card .status-badge.status-rejected').length
        };
        
        Object.entries(counters).forEach(([status, count]) => {
            const counter = document.querySelector(`#${status}-tab .counter`);
            if (counter) {
                counter.textContent = count;
            }
        });
    }
    
    // Initialize tab counters
    updateTabCounters();
    
    // Notification helper function
    function showNotification(type, message) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        const container = document.querySelector('.notifications-container') || document.body;
        container.insertBefore(notification, container.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 150);
        }, 5000);
    }

    // Initialize document preview modal
    document.querySelectorAll('.preview-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const docPath = btn.dataset.docPath;
            const docType = btn.dataset.docType;
            const iframe = document.getElementById('docPreview');

            if (!docPath) return;

            // Adjust iframe height based on document type
            if (docType === 'image') {
                iframe.style.height = '70vh';
            } else if (docType === 'pdf') {
                iframe.style.height = '85vh';
            }

            iframe.src = docPath;
            new bootstrap.Modal('#documentModal').show();
        });
    });

    // Handle document modal close
    document.getElementById('documentModal').addEventListener('hidden.bs.modal', () => {
        document.getElementById('docPreview').src = '';
    });
});
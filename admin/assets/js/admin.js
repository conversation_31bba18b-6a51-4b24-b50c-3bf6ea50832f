// Function to get status badge class based on status
function getStatusBadgeClass(status) {
    if (!status) return 'secondary';
    switch (status.toLowerCase()) {
        case 'active':
            return 'success';
        case 'inactive':
            return 'warning';
        case 'suspended':
            return 'danger';
        default:
            return 'secondary';
    }
}

// Function to show toast notifications
function showToast(title, message, type = 'info') {
    const toastContainer = document.querySelector('.toast-container');
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong><br>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Function to export therapists data
function exportTherapists() {
    const table = $('.table').DataTable();
    const data = table.data().toArray();
    
    let csv = 'Name,Email,Specialization,Experience,Rate,Sessions,Rating,Status\n';
    
    data.forEach(row => {
        const columns = [
            row[0].replace(/["]/g, '""'),  // Name
            row[1],                          // Specialization
            row[2].replace(' years', ''),    // Experience
            row[3].replace('$', ''),         // Rate
            row[4],                          // Sessions
            row[5].replace(' ⭐', ''),       // Rating
            row[6]                           // Status
        ];
        csv += columns.map(col => `"${col}"`).join(',') + '\n';
    });

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', 'therapists_export.csv');
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Save therapist changes
$('#saveTherapistChanges').on('click', function() {
    const formData = new FormData($('#editTherapistForm')[0]);
    
    $.ajax({
        url: 'api/therapists/update.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#editTherapistModal').modal('hide');
            showToast('Success', 'Therapist information updated successfully', 'success');
            setTimeout(() => location.reload(), 1500);
        },
        error: function(xhr) {
            showToast('Error', xhr.responseJSON?.error || 'Failed to update therapist', 'error');
        }
    });
});

// Function to suspend therapist
function suspendTherapist(therapistId) {
    if (confirm('Are you sure you want to suspend this therapist?')) {
        $.post('api/therapists/suspend.php', { therapist_id: therapistId })
            .done(function(response) {
                showToast('Success', 'Therapist has been suspended', 'success');
                setTimeout(() => location.reload(), 1500);
            })
            .fail(function(xhr) {
                showToast('Error', xhr.responseJSON?.error || 'Failed to suspend therapist', 'error');
            });
    }
}
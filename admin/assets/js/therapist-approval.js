class TherapistApprovalManager {
    constructor() {
        this.notificationContainer = document.getElementById('notification-container');
        if (!this.notificationContainer) {
            console.error('Notification container not found');
            return;
        }
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Approve button click handler
        document.querySelectorAll('.approve-btn').forEach(button => {
            button.addEventListener('click', (e) => this.handleApproval(e, 'approve'));
        });

        // Reject button click handler
        document.querySelectorAll('.reject-btn').forEach(button => {
            button.addEventListener('click', (e) => this.handleApproval(e, 'reject'));
        });

        // Review button click handler
        document.querySelectorAll('.review-btn').forEach(button => {
            button.addEventListener('click', (e) => this.handleReview(e));
        });

        // View button click handler
        document.querySelectorAll('.view-btn').forEach(button => {
            button.addEventListener('click', (e) => this.handleView(e));
        });
    }

    async handleApproval(event, action) {
        event.preventDefault();
        const button = event.currentTarget;
        const therapistId = button.getAttribute('data-application-id');
        const card = button.closest('.application-card');

        if (!therapistId) {
            this.showNotification('Error: Invalid therapist ID', 'danger');
            return;
        }

        try {
            button.disabled = true;
            const response = await fetch('process_therapist_approval.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    action: action,
                    therapist_id: parseInt(therapistId)
                })
            });

            let data;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                // Handle non-JSON response
                const text = await response.text();
                console.error('Non-JSON response:', text);
                throw new Error('Server returned an invalid response format');
            }

            if (!response.ok) {
                const errorMessage = data.message || 'An error occurred while processing the request';
                const errorDetails = data.details ? ` (${data.details.file}:${data.details.line})` : '';
                throw new Error(`${errorMessage}${errorDetails}`);
            }

            if (data.success) {
                this.showNotification(data.message, 'success');
                // Move the card to the appropriate tab
                this.moveCardToTab(card, action === 'approve' ? 'approved' : 'rejected');
                // Update counters
                this.updateCounters();
            } else {
                this.showNotification(data.message || 'An error occurred', 'danger');
                button.disabled = false;
            }
        } catch (error) {
            console.error('Error:', error);
            this.showNotification(error.message || 'An error occurred while processing the request', 'danger');
            button.disabled = false;
        }
    }

    async handleReview(event) {
        event.preventDefault();
        const button = event.currentTarget;
        const therapistId = button.getAttribute('data-application-id');
        const card = button.closest('.application-card');

        try {
            button.disabled = true;
            const response = await fetch('process_therapist_approval.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    action: 'review',
                    therapist_id: parseInt(therapistId)
                })
            });

            let data;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                data = await response.json();
            } else {
                // Handle non-JSON response
                const text = await response.text();
                console.error('Non-JSON response:', text);
                throw new Error('Server returned an invalid response format');
            }

            if (!response.ok) {
                const errorMessage = data.message || 'An error occurred while processing the request';
                const errorDetails = data.details ? ` (${data.details.file}:${data.details.line})` : '';
                throw new Error(`${errorMessage}${errorDetails}`);
            }

            if (data.success) {
                this.showNotification(data.message, 'success');
                // Move the card to pending tab
                this.moveCardToTab(card, 'pending');
                // Update counters
                this.updateCounters();
            } else {
                this.showNotification(data.message || 'An error occurred', 'danger');
                button.disabled = false;
            }
        } catch (error) {
            console.error('Error:', error);
            this.showNotification(error.message || 'An error occurred while processing the request', 'danger');
            button.disabled = false;
        }
    }

    handleView(event) {
        event.preventDefault();
        const button = event.currentTarget;
        const therapistId = button.getAttribute('data-application-id');
        
        if (therapistId) {
            window.location.href = `therapist_details.php?id=${therapistId}`;
        }
    }

    moveCardToTab(card, status) {
        if (!card) return;
        
        const targetContainer = document.querySelector(`.applications-container[data-status="${status}"]`);
        if (targetContainer) {
            targetContainer.appendChild(card);
        }
    }

    updateCounters() {
        const counters = {
            pending: document.querySelectorAll('.applications-container[data-status="pending"] .application-card').length,
            approved: document.querySelectorAll('.applications-container[data-status="approved"] .application-card').length,
            rejected: document.querySelectorAll('.applications-container[data-status="rejected"] .application-card').length
        };

        // Update the counters in the tab headers
        const updateCounter = (tabId, count) => {
            const badge = document.querySelector(`#${tabId} .badge`);
            if (badge) {
                badge.textContent = count;
            }
        };

        updateCounter('pending-tab', counters.pending);
        updateCounter('approved-tab', counters.approved);
        updateCounter('rejected-tab', counters.rejected);
        updateCounter('all-tab', counters.pending + counters.approved + counters.rejected);
    }

    showNotification(message, type = 'info') {
        if (!this.notificationContainer) {
            console.error('Notification container not found');
            return;
        }

        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        this.notificationContainer.appendChild(notification);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 150);
        }, 5000);
    }
}

// Initialize the manager when the document is ready
document.addEventListener('DOMContentLoaded', () => {
    window.therapistApprovalManager = new TherapistApprovalManager();
});
<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
require_once 'auth_middleware.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Handle search and filters
$search = isset($_GET['search']) ? $_GET['search'] : '';
$role = isset($_GET['role']) ? $_GET['role'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';

// Build the query
$query = "SELECT * FROM users WHERE 1=1";
$params = [];

if ($search) {
    $query .= " AND (name LIKE ? OR email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($role) {
    $query .= " AND role = ?";
    $params[] = $role;
}

if ($status) {
    $query .= " AND status = ?";
    $params[] = $status;
}

$query .= " ORDER BY created_at DESC";

// Execute query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$users = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en" data-theme="light" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - MindCare Admin</title>
 
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="assets/css/users.css">

    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
   
</head>
<body>
    <button class="theme-toggle" aria-label="Toggle theme" style="position: fixed; top: 20px; right: 20px; z-index: 1000; width: 21px; height: 21px; border-radius: 50%; font-size: 24px; padding: 0; display: flex; align-items: center; justify-content: center;">🌙</button>
    <?php include 'includes/sidebar.php'; ?>

    <div class="admin-container">
        <div class="admin-header">
            <h1>User Management</h1>
            <button class="bulk-btn" onclick="window.location.href='add_user.php'">
                <i class="fas fa-plus"></i> Add New User
            </button>
        </div>

        <div class="filters">
            <div class="filter-group">
                <input type="text" class="search-input" placeholder="Search users..." 
                       value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="filter-group">
                <select class="filter-select">
                    <option value="">All Roles</option>
                    <option value="user" <?php echo $role === 'user' ? 'selected' : ''; ?>>User</option>
                    <option value="therapist" <?php echo $role === 'therapist' ? 'selected' : ''; ?>>Therapist</option>
                </select>
            </div>
            <div class="filter-group">
                <select class="filter-select">
                    <option value="">All Status</option>
                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                </select>
            </div>
        </div>

        <div class="bulk-actions">
            <button class="bulk-btn" onclick="bulkAction('activate')">
                <i class="fas fa-check-circle"></i> Activate Selected
            </button>
            <button class="bulk-btn" onclick="bulkAction('deactivate')">
                <i class="fas fa-times-circle"></i> Deactivate Selected
            </button>
            <button class="bulk-btn" onclick="bulkAction('delete')">
                <i class="fas fa-trash"></i> Delete Selected
            </button>
        </div>

        <table class="users-table">
            <thead>
                <tr>
                    <th class="checkbox-cell">
                        <input type="checkbox" id="selectAll">
                    </th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Joined Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): ?>
                <tr>
                    <td class="checkbox-cell">
                        <input type="checkbox" class="user-select" value="<?php echo $user['id']; ?>">
                    </td>
                    <td><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name'] ?? 'N/A'); ?></td>
                    <td><?php echo htmlspecialchars($user['email'] ?? 'N/A'); ?></td>
                    <td><?php echo ucfirst($user['role'] ?? 'user'); ?></td>
                    <td>
                        <span class="status-badge <?php echo isset($user['active']) && $user['active'] ? 'status-active' : 'status-inactive'; ?>">
                            <?php echo isset($user['active']) && $user['active'] ? 'Active' : 'Inactive'; ?>
                        </span>
                    </td>
                    <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                    <td>
                        <button class="action-btn" onclick="editUser(<?php echo $user['id']; ?>)">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn" onclick="viewUser(<?php echo $user['id']; ?>)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn" onclick="deleteUser(<?php echo $user['id']; ?>)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <script src="../assets/js/theme.js"></script>
    <script>
        // Modal functionality
        function openModal(type, userId) {
            const modal = document.getElementById(`${type}-modal`);
            modal.style.display = 'block';
            
            if (userId) {
                // Show loading state
                const content = modal.querySelector('.modal-content');
                content.innerHTML = '<div class="loading-spinner">Loading user data...</div>';
                
                // Fetch user data and populate form
                fetch(`api/users/get.php?id=${userId}`)
                    .then(response => {
                        if (!response.ok) throw new Error('Network response was not ok');
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            const user = data.user;
                            
                            if (type === 'edit') {
                                content.innerHTML = `
                                    <span class="close" onclick="closeModal('${type}')">&times;</span>
                                    <h2>Edit User</h2>
                                    <form id="edit-user-form" onsubmit="event.preventDefault(); updateUser();">
                                        <input type="hidden" name="id" value="${user.id}">
                                        <div class="form-group">
                                            <label>First Name</label>
                                            <input type="text" name="first_name" value="${user.first_name || ''}" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Last Name</label>
                                            <input type="text" name="last_name" value="${user.last_name || ''}" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Email</label>
                                            <input type="email" name="email" value="${user.email || ''}" required>
                                        </div>
                                        <div class="form-group">
                                            <label>Role</label>
                                            <select name="role" required>
                                                <option value="user" ${user.role === 'user' ? 'selected' : ''}>User</option>
                                                <option value="therapist" ${user.role === 'therapist' ? 'selected' : ''}>Therapist</option>
                                                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Admin</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>New Password (leave empty to keep current)</label>
                                            <input type="password" name="password" placeholder="Enter new password">
                                        </div>
                                        <div class="form-group">
                                            <label>
                                                <input type="checkbox" name="active" ${user.active ? 'checked' : ''}> Active
                                            </label>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Save Changes</button>
                                    </form>
                                `;
                            } else if (type === 'view') {
                                content.innerHTML = `
                                    <span class="close" onclick="closeModal('${type}')">&times;</span>
                                    <h2>User Details</h2>
                                    <div class="user-details scrollable">
                                        <p><strong>Name:</strong> <span class="user-name">${user.first_name} ${user.last_name}</span></p>
                                        <p><strong>Email:</strong> <span class="user-email">${user.email}</span></p>
                                        <p><strong>Role:</strong> <span class="user-role">${user.role}</span></p>
                                        <p><strong>Status:</strong> <span class="user-status">${user.active ? 'Active' : 'Inactive'}</span></p>
                                        <p><strong>Joined:</strong> <span class="user-created">${new Date(user.created_at).toLocaleDateString()}</span></p>
                                    </div>
                                `;
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching user data:', error);
                        content.innerHTML = `<div class="error-message">Error loading user data. Please try again.</div>`;
                    });
            }
        }
        
        function closeModal(type) {
            document.getElementById(`${type}-modal`).style.display = 'none';
        }
        
        // Handle select all checkbox
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.getElementsByClassName('user-select');
            for (let checkbox of checkboxes) {
                checkbox.checked = this.checked;
            }
        });

        // Handle bulk actions
        function bulkAction(action) {
            const selectedUsers = Array.from(document.getElementsByClassName('user-select'))
                .filter(cb => cb.checked)
                .map(cb => cb.value);

            if (selectedUsers.length === 0) {
                alert('Please select users to perform this action');
                return;
            }

            if (confirm(`Are you sure you want to ${action} the selected users?`)) {
                // Send request to server
                fetch(`api/users/bulk_action.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: action,
                        users: selectedUsers
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('Error performing bulk action');
                    }
                });
            }
        }

        // Handle individual user actions
        function editUser(userId) {
            openModal('edit', userId);
        }

        function viewUser(userId) {
            openModal('view', userId);
        }

        function deleteUser(userId) {
            openModal('delete', userId);
        }

        function confirmDelete(userId) {
            if (confirm('Are you sure you want to delete this user?')) {
                fetch(`api/users/delete.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: userId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('Error deleting user');
                    }
                });
            }
        }

        // Handle search and filters
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const searchParams = new URLSearchParams(window.location.search);
            searchParams.set('search', e.target.value);
            window.location.search = searchParams.toString();
        });

        document.querySelectorAll('.filter-select').forEach(select => {
            select.addEventListener('change', function() {
                const searchParams = new URLSearchParams(window.location.search);
                searchParams.set(this.name, this.value);
                window.location.search = searchParams.toString();
            });
        });
    </script>
    
    <!-- Edit User Modal -->
    <div id="edit-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('edit')">&times;</span>
            <h2>Edit User</h2>
            <form id="edit-user-form" onsubmit="event.preventDefault(); updateUser();">
                <input type="hidden" name="id">
                <div class="form-group">
                    <label>First Name</label>
                    <input type="text" name="first_name" required>
                </div>
                <div class="form-group">
                    <label>Last Name</label>
                    <input type="text" name="last_name" required>
                </div>
                <div class="form-group">
                    <label>Email</label>
                    <input type="email" name="email" required>
                </div>
                <div class="form-group">
                    <label>Role</label>
                    <select name="role" required>
                        <option value="user">User</option>
                        <option value="therapist">Therapist</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="active"> Active
                    </label>
                </div>
                <button type="submit" class="btn btn-primary">Save Changes</button>
            </form>
        </div>
    </div>
    
    <!-- View User Modal -->
    <div id="view-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('view')">&times;</span>
            <h2>User Details</h2>
            <div class="user-details">
                <p><strong>Name:</strong> <span class="user-name"></span></p>
                <p><strong>Email:</strong> <span class="user-email"></span></p>
                <p><strong>Role:</strong> <span class="user-role"></span></p>
                <p><strong>Status:</strong> <span class="user-status"></span></p>
                <p><strong>Joined:</strong> <span class="user-created"></span></p>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('delete')">&times;</span>
            <h2>Confirm Delete</h2>
            <p>Are you sure you want to delete this user?</p>
            <div class="modal-actions">
                <button class="btn btn-danger" onclick="confirmDelete(document.querySelector('#delete-modal input[name=\'id\']').value)">Delete</button>
                <button class="btn btn-secondary" onclick="closeModal('delete')">Cancel</button>
            </div>
            <input type="hidden" name="id">
        </div>
    </div>
    
    <style>
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: var(--card-bg);
            margin: 5% auto;
            padding: 1.5rem;
            border-radius: 8px;
            width: 95%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 8px var(--shadow-color);
        }
        
        .users-table {
            width: 100%;
            overflow-x: auto;
            display: block;
        }
        
        .users-table table {
            min-width: 100%;
            border-collapse: collapse;
        }
        
        .form-group {
            margin-bottom: 1rem;
            width: 100%;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            width: auto;
            min-width: 120px;
        }
        
        .modal-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
        }
        
        @media screen and (max-width: 768px) {
            .modal-content {
                margin: 2% auto;
                padding: 1rem;
                width: 98%;
            }
        
            .form-group input,
            .form-group select {
                padding: 0.5rem;
            }
        
            .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }
        
            .modal-actions {
                flex-direction: column;
                gap: 0.5rem;
            }
        
            .users-table th,
            .users-table td {
                padding: 0.5rem;
                font-size: 14px;
            }
        }
        .scrollable {
            max-height: 60vh;
            overflow-y: auto;
            padding-right: 1rem;
        }
        
        .loading-spinner {
            text-align: center;
            padding: 2rem;
        }
        
        .error-message {
            color: var(--danger-color);
            text-align: center;
            padding: 2rem;
        }
        
        .close {
            color: var(--text-secondary);
            float: right;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: var(--text-primary);
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-secondary {
            background-color: var(--text-secondary);
            color: white;
        }
        
        .modal-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .user-details p {
            margin-bottom: 1rem;
        }
    </style>
</body>
</html>

<div id="add-modal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeModal('add')">&times;</span>
        <h2>Add New User</h2>
        <form id="add-user-form" onsubmit="event.preventDefault(); addUser();">
            <div class="form-group">
                <label>First Name</label>
                <input type="text" name="first_name" required>
            </div>
            <div class="form-group">
                <label>Last Name</label>
                <input type="text" name="last_name" required>
            </div>
            <div class="form-group">
                <label>Email</label>
                <input type="email" name="email" required>
            </div>
            <div class="form-group">
                <label>Role</label>
                <select name="role" required>
                    <option value="user">User</option>
                    <option value="therapist">Therapist</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="active" checked> Active
                </label>
            </div>
            <button type="submit" class="btn btn-primary">Add User</button>
        </form>
    </div>
</div>

<script>
function updateUser() {
    const form = document.getElementById('edit-user-form');
    const formData = new FormData(form);
    const data = {
        id: formData.get('id'),
        first_name: formData.get('first_name'),
        last_name: formData.get('last_name'),
        email: formData.get('email'),
        role: formData.get('role'),
        active: formData.get('active') === 'on'
    };

    fetch('api/users/update.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('edit');
            window.location.reload();
        } else {
            alert(data.message || 'Error updating user');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating user');
    });
}

function addUser() {
    const form = document.getElementById('add-user-form');
    const formData = new FormData(form);
    const data = {
        first_name: formData.get('first_name'),
        last_name: formData.get('last_name'),
        email: formData.get('email'),
        role: formData.get('role'),
        active: formData.get('active') === 'on'
    };

    fetch('api/users/add.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal('add');
            alert(`User created successfully! Temporary password: ${data.temp_password}`);
            window.location.reload();
        } else {
            alert(data.message || 'Error creating user');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating user');
    });
}

// Update the "Add New User" button click handler
document.querySelector('.admin-header .bulk-btn').onclick = function() {
    openModal('add');
};
</script>
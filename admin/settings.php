<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
require_once 'auth_middleware.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Fetch system settings
$stmt = $pdo->prepare("SELECT * FROM system_settings LIMIT 1");
$stmt->execute();
$settings = $stmt->fetch(PDO::FETCH_ASSOC);

// Initialize default values if settings don't exist
if (!$settings) {
    $settings = [
        'site_name' => 'MindCare',
        'site_description' => 'Mental Health Care Platform',
        'contact_email' => '<EMAIL>',
        'max_login_attempts' => 5,
        'session_timeout' => 30,
        'two_factor_auth' => 0,
        'maintenance_mode' => 0,
        'maintenance_message' => 'Site is under maintenance. Please check back later.',
        'smtp_host' => '',
        'smtp_port' => 587,
        'smtp_encryption' => 'tls'
    ];
}
?>

<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - MindCare Admin</title>
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="assets/css/appointments.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <button class="theme-toggle" aria-label="Toggle theme" style="position: fixed; top: 20px; right: 20px; z-index: 1000; width: 21px; height: 21px; border-radius: 50%; font-size: 24px; padding: 0; display: flex; align-items: center; justify-content: center;">🌙</button>
    <?php include 'includes/sidebar.php'; ?>

    <div class="admin-container">
        <h1>System Settings</h1>

        <div class="settings-grid">
            <!-- General Settings -->
            <div class="settings-card">
                <h2><i class="fas fa-cog"></i> General Settings</h2>
                <form id="generalSettingsForm">
                    <div class="form-group">
                        <label for="siteName">Site Name</label>
                        <input type="text" id="siteName" name="site_name" 
                               value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label for="siteDescription">Site Description</label>
                        <textarea id="siteDescription" name="site_description" rows="3"><?php echo htmlspecialchars($settings['site_description'] ?? ''); ?></textarea>
                    </div>
                    <div class="form-group">
                        <label for="contactEmail">Contact Email</label>
                        <input type="text" id="contactEmail" name="contact_email" 
                               value="<?php echo htmlspecialchars($settings['contact_email'] ?? ''); ?>">
                    </div>
                </form>
            </div>

            <!-- Security Settings -->
            <div class="settings-card">
                <h2><i class="fas fa-shield-alt"></i> Security Settings</h2>
                <form id="securitySettingsForm">
                    <div class="form-group">
                        <label for="maxLoginAttempts">Max Login Attempts</label>
                        <input type="number" id="maxLoginAttempts" name="max_login_attempts" 
                               value="<?php echo $settings['max_login_attempts'] ?? ''; ?>">
                    </div>
                    <div class="form-group">
                        <label for="sessionTimeout">Session Timeout (minutes)</label>
                        <input type="number" id="sessionTimeout" name="session_timeout" 
                               value="<?php echo $settings['session_timeout'] ?? ''; ?>">
                    </div>
                    <div class="form-group">
                        <label for="twoFactorAuth">Require Two-Factor Authentication</label>
                        <div class="toggle-switch">
                            <input type="checkbox" id="twoFactorAuth" name="two_factor_auth" 
                                   <?php echo isset($settings['two_factor_auth']) && $settings['two_factor_auth'] ? 'checked' : ''; ?>>
                            <span class="toggle-slider"></span>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Maintenance Settings -->
            <div class="settings-card">
                <h2><i class="fas fa-tools"></i> Maintenance Settings</h2>
                <form id="maintenanceSettingsForm">
                    <div class="form-group">
                        <label for="maintenanceMode">Maintenance Mode</label>
                        <div class="toggle-switch">
                            <input type="checkbox" id="maintenanceMode" name="maintenance_mode" 
                                   <?php echo isset($settings['maintenance_mode']) && $settings['maintenance_mode'] ? 'checked' : ''; ?>>
                            <span class="toggle-slider"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="maintenanceMessage">Maintenance Message</label>
                        <textarea id="maintenanceMessage" name="maintenance_message" rows="3"><?php echo htmlspecialchars($settings['maintenance_message'] ?? ''); ?></textarea>
                    </div>
                    <?php if (isset($settings['maintenance_mode']) && $settings['maintenance_mode']): ?>
                    <div class="maintenance-mode">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Maintenance mode is currently active</span>
                    </div>
                    <?php endif; ?>
                </form>
            </div>

            <!-- Email Settings -->
            <div class="settings-card">
                <h2><i class="fas fa-envelope"></i> Email Settings</h2>
                <form id="emailSettingsForm">
                    <div class="form-group">
                        <label for="smtpHost">SMTP Host</label>
                        <input type="text" id="smtpHost" name="smtp_host" 
                               value="<?php echo htmlspecialchars($settings['smtp_host'] ?? ''); ?>">
                    </div>
                    <div class="form-group">
                        <label for="smtpPort">SMTP Port</label>
                        <input type="number" id="smtpPort" name="smtp_port" 
                               value="<?php echo $settings['smtp_port'] ?? ''; ?>">
                    </div>
                    <div class="form-group">
                        <label for="smtpEncryption">SMTP Encryption</label>
                        <select id="smtpEncryption" name="smtp_encryption">
                            <option value="tls" <?php echo isset($settings['smtp_encryption']) && $settings['smtp_encryption'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                            <option value="ssl" <?php echo isset($settings['smtp_encryption']) && $settings['smtp_encryption'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>

        <div class="settings-actions">
            <button type="button" class="btn-save" id="saveSettings">Save All Settings</button>
        </div>
    </div>

    <script src="../assets/js/theme.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const saveSettingsBtn = document.getElementById('saveSettings');
            const forms = [
                document.getElementById('generalSettingsForm'),
                document.getElementById('securitySettingsForm'),
                document.getElementById('maintenanceSettingsForm'),
                document.getElementById('emailSettingsForm')
            ];

            saveSettingsBtn.addEventListener('click', function() {
                const formData = new FormData();
                forms.forEach(form => {
                    const formElements = form.elements;
                    for (let element of formElements) {
                        if (element.type === 'checkbox') {
                            formData.append(element.name, element.checked ? '1' : '0');
                        } else if (element.name) {
                            formData.append(element.name, element.value);
                        }
                    }
                });

                // Send AJAX request to save settings
                fetch('save_settings.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Settings saved successfully!');
                    } else {
                        alert('Error saving settings: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while saving settings.');
                });
            });
        });
    </script>
</body>
</html>
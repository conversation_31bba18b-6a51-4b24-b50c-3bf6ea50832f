<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
require_once 'auth_middleware.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Fetch all therapists with their profile information
$stmt = $pdo->prepare("
SELECT 
    u.id,
    u.username,
    u.email,
    u.created_at,
    tp.specialization,
    tp.experience_years,
    tp.status,
    COALESCE(tp.hourly_rate, 0.00) as hourly_rate,
    COALESCE((SELECT COUNT(*) FROM appointments a WHERE a.therapist_id = u.id), 0) as total_sessions,
    COALESCE((SELECT AVG(rating) FROM therapist_ratings r WHERE r.therapist_id = u.id), 0) as avg_rating
FROM users u
JOIN therapist_profiles tp ON u.id = tp.user_id
WHERE u.role = 'therapist'
ORDER BY u.created_at DESC
");
$stmt->execute();
$therapists = $stmt->fetchAll(PDO::FETCH_ASSOC);

function getStatusBadgeClass($status) {
    switch ($status) {
        case 'active':
            return 'success';
        case 'inactive':
            return 'warning';
        case 'suspended':
            return 'danger';
        default:
            return 'secondary';
    }
}
?>

<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Therapist Management - MindCare Admin</title>
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="assets/css/therapists.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    <button class="theme-toggle" aria-label="Toggle theme">🌙</button>

    <div class='container-fluid'>
        <div class='row'>
            <main class='main-content'>
                <div class='d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4'>
                    <div>
                        <h1 class='h2 mb-1'>Therapist Management</h1>
                        <p class='text-muted'>Manage and monitor all registered therapists</p>
                    </div>
                    <div class='btn-toolbar mb-2 mb-md-0'>
                        <div class='btn-group me-2'>
                            <button type='button' class='btn btn-primary' onclick='exportTherapists()'>
                                <i class='fas fa-download me-2'></i>Export
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class='card mb-4'>
                    <div class='card-body'>
                        <div class='row g-3'>
                            <div class='col-md-3'>
                                <select class='form-select' id='statusFilter'>
                                    <option value=''>All Status</option>
                                    <option value='active'>Active</option>
                                    <option value='inactive'>Inactive</option>
                                    <option value='suspended'>Suspended</option>
                                </select>
                            </div>
                            <div class='col-md-3'>
                                <select class='form-select' id='specializationFilter'>
                                    <option value=''>All Specializations</option>
                                    <option value='Clinical Psychology'>Clinical Psychology</option>
                                    <option value='Counseling'>Counseling</option>
                                    <option value='Marriage & Family'>Marriage & Family</option>
                                </select>
                            </div>
                            <div class='col-md-3'>
                                <select class='form-select' id='experienceFilter'>
                                    <option value=''>All Experience Levels</option>
                                    <option value='1'>1+ Years</option>
                                    <option value='3'>3+ Years</option>
                                    <option value='5'>5+ Years</option>
                                    <option value='10'>10+ Years</option>
                                </select>
                            </div>
                            <div class='col-md-3'>
                                <input type='text' class='form-control' id='searchFilter' placeholder='Search...'>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Therapists Table -->
                <div class='card'>
                    <div class='card-body'>
                        <div class='table-responsive'>
                            <table class='table table-hover'>
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Specialization</th>
                                        <th>Experience</th>
                                        <th>Rate/Hour</th>
                                        <th>Sessions</th>
                                        <th>Rating</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($therapists as $therapist): ?>
                                        <tr>
                                            <td>
                                                <div class='d-flex align-items-center'>
                                                    <img src='../uploads/therapists/<?php echo $therapist["id"]; ?>/profile.jpg' 
                                                         class='rounded-circle me-2 profile-image' 
                                                         width='40' 
                                                         height='40' 
                                                         onerror="this.src='../assets/images/default-image.jpg'">
                                                    <div>
                                                        <div class='fw-bold'><?php echo htmlspecialchars($therapist['username']); ?></div>
                                                        <div class='small text-muted'><?php echo htmlspecialchars($therapist['email']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($therapist['specialization']); ?></td>
                                            <td><?php echo $therapist['experience_years']; ?> years</td>
                                            <td>KSH <?php echo number_format($therapist['hourly_rate'], 2); ?></td>
                                            <td><?php echo $therapist['total_sessions']; ?></td>
                                            <td>
                                                <?php if ($therapist['avg_rating']): ?>
                                                    <div class="rating-stars">
                                                        <?php echo number_format($therapist['avg_rating'], 1); ?>
                                                        <i class='fas fa-star'></i>
                                                    </div>
                                                <?php else: ?>
                                                    N/A
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class='badge bg-<?php echo getStatusBadgeClass($therapist['status']); ?>'>
                                                    <?php echo ucfirst($therapist['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class='btn-group'>
                                                    <button class='btn btn-sm btn-info me-2' onclick='viewProfile(<?php echo $therapist["id"]; ?>)'>
                                                        <i class='fas fa-eye'></i>
                                                    </button>
                                                    <button class='btn btn-sm btn-warning me-2' onclick='editStatus(<?php echo $therapist["id"]; ?>)'>
                                                        <i class='fas fa-edit'></i>
                                                    </button>
                                                    <button class='btn btn-sm btn-danger' onclick='suspendTherapist(<?php echo $therapist["id"]; ?>)'>
                                                        <i class='fas fa-ban'></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Edit Therapist Modal -->
    <div class="modal fade" id="editTherapistModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Therapist</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editTherapistForm">
                        <input type="hidden" id="editTherapistId" name="therapist_id">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Status</label>
                                <select class="form-select" id="editStatus" name="status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="suspended">Suspended</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Hourly Rate (KSH)</label>
                                <input type="number" class="form-control" id="editHourlyRate" name="hourly_rate" min="0" step="0.01">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveTherapistChanges()">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script src="assets/js/therapists.js"></script>
</body>
</html>
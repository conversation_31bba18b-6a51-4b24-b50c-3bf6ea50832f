<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Log the logout action if admin is logged in
if (isset($_SESSION['admin_id'])) {
    $log_stmt = $pdo->prepare("INSERT INTO admin_logs (admin_id, action, ip_address) VALUES (?, 'logout', ?)");
    $log_stmt->execute([$_SESSION['admin_id'], $_SERVER['REMOTE_ADDR']]);
}

// Clear all session variables
$_SESSION = array();

// Destroy the session cookie
if (isset($_COOKIE[session_name()])) {
    setcookie(session_name(), '', time() - 3600, '/');
}

// Destroy the session
session_destroy();

// Redirect to login page
header('Location: login.php');
exit();
<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
require_once 'auth_middleware.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Fetch all therapist applications
$stmt = $pdo->prepare("SELECT tp.*, tp.id as therapist_id, u.email as user_email, COALESCE(tp.experience_years, 0) as years_experience FROM therapist_profiles tp LEFT JOIN users u ON tp.user_id = u.id ORDER BY u.created_at DESC");
$stmt->execute();
$all_applications = $stmt->fetchAll();

// Separate applications by status
$pending_applications = array_filter($all_applications, function($app) {
    return $app['status'] === 'pending';
});

$approved_applications = array_filter($all_applications, function($app) {
    return $app['status'] === 'approved';
});

$rejected_applications = array_filter($all_applications, function($app) {
    return $app['status'] === 'rejected';
});

// Fetch system settings
$stmt = $pdo->prepare("SELECT * FROM system_settings LIMIT 1");
$stmt->execute();
$settings = $stmt->fetch();
?>

<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Therapist Approvals - MindCare Admin</title>
    <link rel="stylesheet" href="admin.css">
    
    <link rel="stylesheet" href="assets/css/therapist_approval.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>
<body>
    <button class="theme-toggle" aria-label="Toggle theme" style="position: fixed; top: 20px; right: 20px; z-index: 1000; width: 21px; height: 21px; border-radius: 50%; font-size: 24px; padding: 0; display: flex; align-items: center; justify-content: center;">🌙</button>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="content">

    <div class='container-fluid approval-container'>
        <main class='col main-content'>
            <div class='d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4'>
                <div>
                    <h1 class='h2 mb-1'>Therapist Applications</h1>
                    <p class='text-muted'>Review and manage therapist applications</p>
                </div>
                <div class='btn-toolbar mb-2 mb-md-0'>
                    <div class='btn-group me-2'>
                        <button type='button' class='btn btn-primary'>
                            <i class='fas fa-download me-2'></i>Export
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <ul class='nav nav-tabs mb-4 approval-tabs'>
                <li class='nav-item'>
                    <a class='nav-link active' id='pending-tab' href='#pending' data-bs-toggle='tab'>Pending Review <span class="counter"><?= count($pending_applications) ?></span></a>
                </li>
                <li class='nav-item'>
                    <a class='nav-link' id='approved-tab' href='#approved' data-bs-toggle='tab'>Approved <span class="counter"><?= count($approved_applications) ?></span></a>
                </li>
                <li class='nav-item'>
                    <a class='nav-link' id='rejected-tab' href='#rejected' data-bs-toggle='tab'>Rejected <span class="counter"><?= count($rejected_applications) ?></span></a>
                </li>
                <li class='nav-item'>
                    <a class='nav-link' id='all-tab' href='#all' data-bs-toggle='tab'>All Applications <span class="counter"><?= count($all_applications) ?></span></a>
                </li>
            </ul>

            <div class='tab-content'>
                <!-- Pending Applications Tab -->
                <div class='tab-pane fade show active' id='pending'>
                    <?php if(empty($pending_applications)): ?>
                        <div class='alert alert-info'>No pending applications</div>
                    <?php else: ?>
                        <div class='row applications-container'>
                            <?php foreach($pending_applications as $app): ?>
                                <?php include './includes/approval-cards.php'; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Approved Applications Tab -->
                <div class='tab-pane fade' id='approved'>
                    <?php if(empty($approved_applications)): ?>
                        <div class='alert alert-info'>No approved applications</div>
                    <?php else: ?>
                        <div class='row applications-container'>
                            <?php foreach($approved_applications as $app): ?>
                                <?php include './includes/approval-cards.php'; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Rejected Applications Tab -->
                <div class='tab-pane fade' id='rejected'>
                    <?php if(empty($rejected_applications)): ?>
                        <div class='alert alert-info'>No rejected applications</div>
                    <?php else: ?>
                        <div class='row applications-container'>
                            <?php foreach($rejected_applications as $app): ?>
                                <?php include './includes/approval-cards.php'; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- All Applications Tab -->
                <div class='tab-pane fade' id='all'>
                    <?php if(empty($all_applications)): ?>
                        <div class='alert alert-info'>No applications found</div>
                    <?php else: ?>
                        <div class='row applications-container'>
                            <?php foreach($all_applications as $app): ?>
                                <?php include './includes/approval-cards.php'; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
    <!-- Document Preview Modal -->
<div class='modal fade document-preview-modal' id='documentModal' tabindex='-1'>
    <div class='modal-dialog modal-xl'>
        <div class='modal-content'>
            <div class='modal-header'>
                <h5 class='modal-title'>Document Preview</h5>
                <button type='button' class='btn-close' data-bs-dismiss='modal'></button>
            </div>
            <div class='modal-body'>
                <iframe id='docPreview' class='w-100' style='height: 70vh'></iframe>
            </div>
        </div>
    </div>
</div>
</div>

    <!-- Notifications Container -->
    <div id="notification-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script src="assets/js/therapist-approval.js"></script>
</body>
</html>
<?php
// Start output buffering to prevent any unwanted output
ob_start();

// Disable error display to prevent HTML errors from being output
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set error handler to catch all errors
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("PHP Error [$errno]: $errstr in $errfile on line $errline");
    return true;
});

// Set exception handler to catch all exceptions
set_exception_handler(function($e) {
    error_log("Uncaught Exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'An internal server error occurred: ' . $e->getMessage(),
        'error' => true,
        'details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
    exit();
});

require_once '../config/config.php';
require_once './auth_middleware.php';
require_once __DIR__ . '/../auth/EmailHandler.php';

// Ensure we're sending JSON response
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('X-Content-Type-Options: nosniff');

// Check if it's an AJAX request
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request type']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Validate admin permissions
if (!isset($_SESSION['admin_role']) || $_SESSION['admin_role'] !== 'admin' || !isset($_SESSION['admin_is_authenticated']) || !$_SESSION['admin_is_authenticated']) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Parse JSON input
$rawInput = file_get_contents('php://input');
error_log("Raw Input: " . $rawInput); // Log raw input

$input = json_decode($rawInput, true);
error_log("Decoded Input: " . print_r($input, true)); // Log decoded input

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid JSON data received', 'raw_input' => $rawInput]);
    exit();
}

// Validate action
if (!isset($input['action']) || !in_array($input['action'], ['approve', 'reject', 'review'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid action specified']);
    exit();
}

// Validate therapist_id
if (!isset($input['therapist_id']) || !is_numeric($input['therapist_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid therapist ID']);
    exit();
}

try {
    $pdo->beginTransaction();

    // Get therapist details first
    $stmt = $pdo->prepare("
        SELECT 
            tp.id, 
            tp.user_id,
            tp.status as current_status,
            u.email, 
            u.first_name,
            u.last_name,
            CONCAT(u.first_name, ' ', u.last_name) as name 
        FROM therapist_profiles tp 
        JOIN users u ON u.id = tp.user_id 
        WHERE tp.id = ?
    ");
    $stmt->execute([$input['therapist_id']]);
    $therapist = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$therapist) {
        throw new Exception('Therapist profile not found');
    }

    // Check if the action is valid for the current status
    $action = $input['action'];
    $currentStatus = $therapist['current_status'];
    
    // For review action, we only allow it on approved or rejected applications
    if ($action === 'review' && !in_array($currentStatus, ['approved', 'rejected'])) {
        throw new Exception('Cannot review an application that is not approved or rejected');
    }
    
    // For approve/reject actions, we only allow them on pending applications
    if (in_array($action, ['approve', 'reject']) && $currentStatus !== 'pending') {
        throw new Exception('This application has already been processed');
    }

    // Update therapist profile status
    $stmt = $pdo->prepare("
        UPDATE therapist_profiles 
        SET 
            status = ?,
            updated_at = NOW()
        WHERE id = ?
    ");
    
    $status = ($action === 'approve') ? 'approved' : ($action === 'reject' ? 'rejected' : 'pending');
    
    $stmt->execute([
        $status,
        $input['therapist_id']
    ]);

    if ($stmt->rowCount() === 0) {
        throw new Exception('Failed to update therapist status');
    }

    // If approved, update user role and status
    if ($action === 'approve') {
        // Update user role and status
        $stmt = $pdo->prepare("
            UPDATE users 
            SET 
                role = 'therapist',
                active = 1,
                updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$therapist['user_id']]);

        if ($stmt->rowCount() === 0) {
            throw new Exception('Failed to update user role');
        }

        // Send approval notification email
        $emailSent = false;
        try {
            error_log("Attempting to send approval email to: " . $therapist['email']);
            $emailHandler = new EmailHandler();
            $emailSent = $emailHandler->sendTherapistApprovalEmail($therapist['email'], $therapist['name']);
            error_log("Approval email sent successfully: " . ($emailSent ? "Yes" : "No"));
        } catch (Exception $e) {
            error_log('Failed to send approval email: ' . $e->getMessage());
            error_log('Email error trace: ' . $e->getTraceAsString());
            // Continue with the approval process even if email fails
        }
    } else if ($action === 'reject') {
        // Send rejection notification email
        $emailSent = false;
        try {
            error_log("Attempting to send rejection email to: " . $therapist['email']);
            $emailHandler = new EmailHandler();
            $emailSent = $emailHandler->sendTherapistRejectionEmail($therapist['email'], $therapist['name']);
            error_log("Rejection email sent successfully: " . ($emailSent ? "Yes" : "No"));
        } catch (Exception $e) {
            error_log('Failed to send rejection email: ' . $e->getMessage());
            error_log('Email error trace: ' . $e->getTraceAsString());
            // Continue with the rejection process even if email fails
        }
    } else if ($action === 'review') {
        // If moving back to pending, we need to update the user role if it was approved
        if ($currentStatus === 'approved') {
            // Update user role back to client
            $stmt = $pdo->prepare("
                UPDATE users 
                SET 
                    role = 'client',
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$therapist['user_id']]);
            
            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update user role');
            }
        }
        
        // Send review notification email
        $emailSent = false;
        try {
            error_log("Attempting to send review email to: " . $therapist['email']);
            $emailHandler = new EmailHandler();
            $emailSent = $emailHandler->sendTherapistReviewEmail($therapist['email'], $therapist['name']);
            error_log("Review email sent successfully: " . ($emailSent ? "Yes" : "No"));
        } catch (Exception $e) {
            error_log('Failed to send review email: ' . $e->getMessage());
            error_log('Email error trace: ' . $e->getTraceAsString());
            // Continue with the review process even if email fails
        }
    }

    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => "Application {$status} successfully",
        'status' => $status,
        'email_sent' => isset($emailSent) ? $emailSent : null
    ]);
    exit();

} catch (Exception $e) {
    $pdo->rollBack();
    error_log('Approval Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
    error_log('Stack trace: ' . $e->getTraceAsString());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error' => true,
        'details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
    exit();
}
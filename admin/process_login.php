<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
require_once '../config/auth_middleware.php';
require_once '../auth/AuthHandler.php';

if (session_status() === PHP_SESSION_NONE) {
    session_name('admin_session');
    session_start();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: login.php');
    exit();
}

$username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING);
$password = $_POST['password'] ?? '';

// Validate input
if (empty($username) || empty($password)) {
    $_SESSION['error'] = 'Please enter both username and password.';
    header('Location: login.php');
    exit();
}

try {
    $auth = new AuthHandler($pdo);
    $result = $auth->login($username, $password, 'admin');

    if ($result['success']) {
        // Regenerate session ID for security
        session_regenerate_id(true);

        // Log successful login
        $log_stmt = $pdo->prepare("INSERT INTO admin_logs (admin_id, username, action, status, ip_address) VALUES (?, ?, 'login', 'success', ?)");
        $log_stmt->execute([$result['user_id'], $username, $_SERVER['REMOTE_ADDR']]);

        // Redirect to dashboard or requested page
        $redirect_url = $_SESSION['admin_redirect_url'] ?? 'dashboard.php';
        unset($_SESSION['admin_redirect_url']);
        header('Location: ' . $redirect_url);
        exit();
    } else {
        // Log failed login attempt
        $log_stmt = $pdo->prepare("INSERT INTO admin_logs (admin_id, username, action, ip_address, status) VALUES (NULL, ?, 'login', ?, 'failure')");
        $log_stmt->execute([$username, $_SERVER['REMOTE_ADDR']]);

        $_SESSION['error'] = 'Invalid username or password.';
        header('Location: login.php');
        exit();
    }
} catch (PDOException $e) {
    error_log('Admin login error: ' . $e->getMessage());
    $_SESSION['error'] = 'Login temporarily unavailable. Our team has been notified.';
    header('Location: login.php');
    exit();
}
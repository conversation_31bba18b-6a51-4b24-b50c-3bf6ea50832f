:root[data-theme="light"] {
    --primary-color: #7C65B3;
    --secondary-color: #9D84D2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --text-primary: #333333;
    --text-secondary: #666666;
    --bg-primary: #ffffff;
    --bg-secondary: #f5f7fa;
    --border-color: #e1e1e1;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --card-bg: #ffffff;
    --table-header-bg: #f8f8f8;
    --table-hover: #f5f7fa;
}

:root[data-theme="dark"] {
    --primary-color: #9D84D2;
    --secondary-color: #7C65B3;
    --success-color: #2ea043;
    --danger-color: #da3633;
    --warning-color: #d29922;
    --text-primary: #E1E1E6;
    --text-secondary: #A1A1AA;
    --bg-primary: #252541;
    --bg-secondary: #1a1a2e;
    --border-color: #333355;
    --shadow-color: rgba(0, 0, 0, 0.2);
    --card-bg: #252541;
    --table-header-bg: #1E1E32;
    --table-hover: #2A2A4A;
}

body {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.admin-container {
    padding: 2rem;
    margin-left: 300px;
    transition: margin-left 0.3s ease;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background-color: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.admin-header h1 {
    font-size: 1.8rem;
    color: var(--text-primary);
    margin: 0;
}

.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.search-input,
.filter-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.bulk-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.bulk-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    background-color: var(--primary-color);
    color: white;
}

.bulk-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px var(--shadow-color);
}

.users-table, .table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--card-bg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px var(--shadow-color);
    color: var(--text-primary);
}

.users-table th,
.users-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.users-table th, .table th {
    background-color: var(--table-header-bg);
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
}

.users-table tr:hover, .table tr:hover {
    background-color: var(--table-hover);
}

.checkbox-cell {
    width: 40px;
}

.status-badge, .badge {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    color: white;
}

.status-active, .bg-success {
    background-color: var(--success-color);
}

.status-inactive, .bg-warning {
    background-color: var(--warning-color);
}

.bg-danger {
    background-color: var(--danger-color);
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    color: var(--primary-color);
    background-color: rgba(74, 144, 226, 0.1);
}

@media (max-width: 1024px) {
    .admin-container {
        margin-left: 0;
        padding: 1rem;
    }

    .filters,
    .bulk-actions {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .admin-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}


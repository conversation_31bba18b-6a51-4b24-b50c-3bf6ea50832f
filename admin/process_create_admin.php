<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}



if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: create_admin.php');
    exit();
}

// Get and sanitize input data
$username = trim(filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING));
$name = trim(filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING));
$email = trim(filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL));
$password = $_POST['password'];
$confirm_password = $_POST['confirm_password'];

// Validate input
if (empty($username) || empty($name) || empty($email) || empty($password) || empty($confirm_password)) {
    $_SESSION['error'] = 'All fields are required.';
    header('Location: create_admin.php');
    exit();
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $_SESSION['error'] = 'Invalid email format.';
    header('Location: create_admin.php');
    exit();
}

// Validate password match
if ($password !== $confirm_password) {
    $_SESSION['error'] = 'Passwords do not match.';
    header('Location: create_admin.php');
    exit();
}



try {
    // Check if username already exists
    $stmt = $pdo->prepare('SELECT id FROM admins WHERE username = ?');
    $stmt->execute([$username]);
    if ($stmt->fetch()) {
        $_SESSION['error'] = 'Username already exists.';
        header('Location: create_admin.php');
        exit();
    }

    // Check if email already exists
    $stmt = $pdo->prepare('SELECT id, email FROM admins WHERE BINARY email = ?');
    $stmt->execute([$email]);
    $existing_user = $stmt->fetch();

    // Debug logging
    error_log("Email check - Input email: " . $email);
    if ($existing_user) {
        error_log("Email check - Found existing email: " . $existing_user['email']);
        $_SESSION['error'] = 'Email already exists.';
        header('Location: create_admin.php');
        exit();
    }

    // Hash password
    $password_hash = password_hash($password, PASSWORD_DEFAULT);

    // Insert new admin
    $stmt = $pdo->prepare('INSERT INTO admins (username, password_hash, name, email) VALUES (?, ?, ?, ?)');
    $stmt->execute([$username, $password_hash, $name, $email]);


    // Log the admin creation
    $admin_id = $pdo->lastInsertId();
    $creating_admin_id = $_SESSION['admin_id'];
    $action_details = "Created new admin account: $username";
    $stmt = $pdo->prepare('INSERT INTO admin_logs (admin_id, username, action, status, action_details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)');
    $stmt->execute([$creating_admin_id, $username, 'create', 'success', $action_details, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']]);

    $_SESSION['success'] = 'Admin account created successfully.';
    header('Location: users.php');
    exit();
} catch (PDOException $e) {
    error_log('Admin creation error: ' . $e->getMessage());
    $_SESSION['error'] = 'An error occurred while creating the admin account. Please try again.';
    header('Location: create_admin.php');
    exit();
}
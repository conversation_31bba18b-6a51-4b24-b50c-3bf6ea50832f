<?php
require_once 'auth_middleware.php';
require_once '../config/config.php';

// Fetch payments with related information
$stmt = $pdo->prepare("
SELECT 
    p.id,
    p.amount,
    p.transaction_id,
    p.payment_method,
    p.payment_status as status,
    p.created_at,
    u.username as client_name
FROM payments p
JOIN users u ON p.user_id = u.id
ORDER BY p.created_at DESC
");
$stmt->execute();
$payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate total revenue
$total_revenue = array_reduce($payments, function($carry, $payment) {
    return $carry + ($payment['status'] === 'completed' ? $payment['amount'] : 0);
}, 0);
?>

<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Management - MindCare Admin</title>
    <link rel="stylesheet" href="admin.css">
    <link rel="stylesheet" href="assets/css/appointments.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <button class="theme-toggle" aria-label="Toggle theme" style="position: fixed; top: 20px; right: 20px; z-index: 1000; width: 21px; height: 21px; border-radius: 50%; font-size: 24px; padding: 0; display: flex; align-items: center; justify-content: center;">🌙</button>

    <?php include 'includes/sidebar.php'; ?>

    <div class="admin-container">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
            <div>
                <h1 class="h2 mb-1">Payment Management</h1>
                <p class="text-muted">View and manage payment transactions</p>
            </div>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-primary" onclick="exportToCSV()">
                        <i class="fas fa-download me-2"></i>Export to CSV
                    </button>
                </div>
            </div>
        </div>

        <!-- Revenue Overview -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Total Revenue</h6>
                        <h2 class="card-title mb-0">KSH <?php echo number_format($total_revenue, 2); ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Total Transactions</h6>
                        <h2 class="card-title mb-0"><?php echo count($payments); ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Average Transaction</h6>
                        <h2 class="card-title mb-0">KSH <?php echo number_format($total_revenue / max(1, count($payments)), 2); ?></h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title mb-3">Filter Transactions</h5>
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" id="date_from" name="date_from" class="form-control" 
                               value="<?php echo $_GET['date_from'] ?? ''; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" id="date_to" name="date_to" class="form-control"
                               value="<?php echo $_GET['date_to'] ?? ''; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="completed" <?php echo ($_GET['status'] ?? '') === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="pending" <?php echo ($_GET['status'] ?? '') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="failed" <?php echo ($_GET['status'] ?? '') === 'failed' ? 'selected' : ''; ?>>Failed</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select id="payment_method" name="payment_method" class="form-select">
                            <option value="">All Methods</option>
                            <option value="mpesa" <?php echo ($_GET['payment_method'] ?? '') === 'mpesa' ? 'selected' : ''; ?>>M-Pesa</option>
                            <option value="bank" <?php echo ($_GET['payment_method'] ?? '') === 'bank' ? 'selected' : ''; ?>>Bank Transfer</option>
                            <option value="paypal" <?php echo ($_GET['payment_method'] ?? '') === 'paypal' ? 'selected' : ''; ?>>PayPal</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-2"></i>Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Payments Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Transaction ID</th>
                                <th>Date</th>
                                <th>Client</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payments as $payment): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($payment['transaction_id']); ?></td>
                                <td><?php echo date('M d, Y H:i', strtotime($payment['created_at'])); ?></td>
                                <td><?php echo htmlspecialchars($payment['client_name']); ?></td>
                                <td>KSH <?php echo number_format($payment['amount'], 2); ?></td>
                                <td>
                                    <?php
                                    $method_class = '';
                                    switch($payment['payment_method']) {
                                        case 'mpesa':
                                            $method_class = 'text-success';
                                            break;
                                        case 'bank':
                                            $method_class = 'text-primary';
                                            break;
                                        case 'paypal':
                                            $method_class = 'text-info';
                                            break;
                                    }
                                    ?>
                                    <span class="<?php echo $method_class; ?>">
                                        <?php echo ucfirst($payment['payment_method']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    switch($payment['status']) {
                                        case 'completed':
                                            $status_class = 'badge bg-success';
                                            break;
                                        case 'pending':
                                            $status_class = 'badge bg-warning';
                                            break;
                                        case 'failed':
                                            $status_class = 'badge bg-danger';
                                            break;
                                    }
                                    ?>
                                    <span class="<?php echo $status_class; ?>">
                                        <?php echo ucfirst($payment['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="viewTransaction('<?php echo $payment['transaction_id']; ?>')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/theme.js"></script>
    <script>
        function viewTransaction(transactionId) {
            // Implement transaction view logic
            console.log('Viewing transaction:', transactionId);
        }

        function exportToCSV() {
            // Implement CSV export logic
            console.log('Exporting to CSV...');
        }
    </script>
</body>
</html>
<div class="admin-sidebar">
    <div class="sidebar-header">
        <img src="../assets/images/dark-logo.png" alt="MindCare Logo" class="sidebar-logo">
        <h2>Admin Panel</h2>
    </div>
    
    <nav class="sidebar-nav">
        <a href="dashboard.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) === 'dashboard.php' ? 'active' : ''; ?>">
            <i class="fas fa-chart-line"></i>
            <span>Dashboard</span>
        </a>
        <a href="users.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) === 'users.php' ? 'active' : ''; ?>">
            <i class="fas fa-users"></i>
            <span>User Management</span>
        </a>
        <a href="therapist_approval.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) === 'therapist_approval.php' ? 'active' : ''; ?>">
            <i class="fas fa-user-check"></i>
            <span>Therapist Approvals</span>
        </a>
        <a href="therapists.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) === 'therapists.php' ? 'active' : ''; ?>">
            <i class="fas fa-user-md"></i>
            <span>Therapist Management</span>
        </a>
        <a href="appointments.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) === 'appointments.php' ? 'active' : ''; ?>">
            <i class="fas fa-calendar-check"></i>
            <span>Appointments</span>
        </a>
        <a href="payments.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) === 'payments.php' ? 'active' : ''; ?>">
            <i class="fas fa-credit-card"></i>
            <span>Payment History</span>
        </a>
        <a href="reports.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) === 'reports.php' ? 'active' : ''; ?>">
            <i class="fas fa-chart-bar"></i>
            <span>Reports & Analytics</span>
        </a>
        <a href="settings.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'active' : ''; ?>">
            <i class="fas fa-cog"></i>
            <span>System Settings</span>
        </a>
    </nav>

    <div class="sidebar-footer">
        <a href="profile.php" class="nav-item <?php echo basename($_SERVER['PHP_SELF']) === 'profile.php' ? 'active' : ''; ?>">
            <i class="fas fa-user-circle"></i>
            <span>Admin Profile</span>
        </a>
        <a href="logout.php" class="nav-item">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
        </a>

        <div>
            <br>
          

        </div>
    </div>
</div>

<style>
.admin-sidebar {
    width: 280px;
    height: 100vh;
    background: var(--bg-color);
    border-right: 1px solid var(--border-color);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    transition: all 0.3s ease;
    overflow-y: auto;
}

.sidebar-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.sidebar-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.sidebar-header h2 {
    color: var(--text-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.sidebar-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-item:hover {
    background: var(--hover-color);
}

.nav-item.active {
    background: var(--primary-color);
    color: white;
}

.nav-item i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.nav-item span {
    font-size: 0.95rem;
    font-weight: 500;
}

.sidebar-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const adminSidebar = document.querySelector('.admin-sidebar');

    if (mobileMenuToggle && adminSidebar) {
        mobileMenuToggle.addEventListener('click', function() {
            adminSidebar.classList.toggle('active');
        });
    }
});
</script>
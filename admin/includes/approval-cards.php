<?php
$app = $app;
$uploads_base = '../uploads/therapists/' . $app['therapist_id'] . '/';

// Get the status badge class based on the application status
$statusBadgeClass = match($app['status']) {
    'pending' => 'bg-warning',
    'approved' => 'bg-success',
    'rejected' => 'bg-danger',
    default => 'bg-secondary'
};

// Get the status text
$statusText = ucfirst($app['status']);

// Get the action buttons based on status
$actionButtons = match($app['status']) {
    'pending' => '
        <button class="btn btn-success btn-sm approve-btn" data-application-id="' . $app['therapist_id'] . '">
            <i class="fas fa-check"></i> Approve
        </button>
        <button class="btn btn-danger btn-sm reject-btn" data-application-id="' . $app['therapist_id'] . '">
            <i class="fas fa-times"></i> Reject
        </button>
    ',
    'approved' => '
        <button class="btn btn-info btn-sm view-btn" data-application-id="' . $app['therapist_id'] . '">
            <i class="fas fa-eye"></i> View Details
        </button>
        <button class="btn btn-warning btn-sm review-btn" data-application-id="' . $app['therapist_id'] . '">
            <i class="fas fa-redo"></i> Review Again
        </button>
    ',
    'rejected' => '
        <button class="btn btn-warning btn-sm review-btn" data-application-id="' . $app['therapist_id'] . '">
            <i class="fas fa-redo"></i> Review Again
        </button>
    ',
    default => ''
};
?>

<div class='col-md-6 col-lg-4 mb-4'>
    <div class='card application-card' data-application-id="<?= $app['therapist_id'] ?>">
        <div class='card-header'>
            <div class='d-flex justify-content-between align-items-start mb-2'>
                <h5 class='mb-0'><?= htmlspecialchars($app['first_name'] . ' ' . $app['last_name']) ?></h5>
                <span class='badge <?= $statusBadgeClass ?>'>
                    <?= $statusText ?>
                </span>
            </div>
            <div class='d-flex justify-content-between align-items-center'>
                <small class='text-muted'><i class='fas fa-calendar-alt me-1'></i> <?= date('M d, Y', strtotime($app['created_at'])) ?></small>
                <small class='text-muted'><i class='fas fa-envelope me-1'></i> <?= htmlspecialchars($app['email']) ?></small>
            </div>
        </div>
        
        <div class='card-body'>
            <div class='mb-3'>
                <label class='text-muted small d-block mb-1'><i class='fas fa-stethoscope me-1'></i> Specialization</label>
                <p class='mb-0'><?= htmlspecialchars($app['specialization']) ?></p>
            </div>
            <div class='mb-3'>
                <label class='text-muted small d-block mb-1'><i class='fas fa-briefcase me-1'></i> Experience</label>
                <p class='mb-0'><?= htmlspecialchars($app['years_experience']) ?> years</p>
            </div>
            
            <div class='document-preview-tabs'>
                <label class='text-muted small d-block mb-2'><i class='fas fa-file-alt me-1'></i> Documents</label>
                <div class='document-list'>
                    <div class='document-item mb-2'>
                        <div class='d-flex align-items-center justify-content-between bg-light p-2 rounded'>
                            <button class='btn btn-link text-decoration-none' data-bs-toggle='tab' 
                                data-doc-type='image' 
                                data-doc-path='<?= $uploads_base . $app['profile_image'] ?>'>
                                <i class='fas fa-user-circle me-1'></i> Profile Photo
                            </button>
                            <div class='btn-group'>
                                <button class='btn btn-sm btn-outline-primary preview-btn' 
                                    data-doc-type='image' 
                                    data-doc-path='<?= $uploads_base . $app['profile_image'] ?>'>
                                    <i class='fas fa-eye'></i>
                                </button>
                                <a href='<?= $uploads_base . $app['profile_image'] ?>' 
                                   class='btn btn-sm btn-outline-success' 
                                   download>
                                    <i class='fas fa-download'></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class='document-item mb-2'>
                        <div class='d-flex align-items-center justify-content-between bg-light p-2 rounded'>
                            <button class='btn btn-link text-decoration-none' data-bs-toggle='tab'
                                data-doc-type='pdf'
                                data-doc-path='<?= $uploads_base . $app['license_doc_path'] ?>'>
                                <i class='fas fa-certificate me-1'></i> License Document
                            </button>
                            <div class='btn-group'>
                                <button class='btn btn-sm btn-outline-primary preview-btn' 
                                    data-doc-type='pdf'
                                    data-doc-path='<?= $uploads_base . $app['license_doc_path'] ?>'>
                                    <i class='fas fa-eye'></i>
                                </button>
                                <a href='<?= $uploads_base . $app['license_doc_path'] ?>' 
                                   class='btn btn-sm btn-outline-success' 
                                   download>
                                    <i class='fas fa-download'></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class='document-item mb-2'>
                        <div class='d-flex align-items-center justify-content-between bg-light p-2 rounded'>
                            <button class='btn btn-link text-decoration-none' data-bs-toggle='tab'
                                data-doc-type='pdf'
                                data-doc-path='<?= $uploads_base . $app['cv_path'] ?>'>
                                <i class='fas fa-file-pdf me-1'></i> Curriculum Vitae
                            </button>
                            <div class='btn-group'>
                                <button class='btn btn-sm btn-outline-primary preview-btn' 
                                    data-doc-type='pdf'
                                    data-doc-path='<?= $uploads_base . $app['cv_path'] ?>'>
                                    <i class='fas fa-eye'></i>
                                </button>
                                <a href='<?= $uploads_base . $app['cv_path'] ?>' 
                                   class='btn btn-sm btn-outline-success' 
                                   download>
                                    <i class='fas fa-download'></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class='card-footer'>
            <div class='btn-group w-100'>
                <?= $actionButtons ?>
            </div>
        </div>
    </div>
</div>
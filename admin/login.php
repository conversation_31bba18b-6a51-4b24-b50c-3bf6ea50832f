<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION['admin_id'])) {
    header('Location: dashboard.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - MindCare</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <style>
        .admin-auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: var(--bg-gradient);
            padding: 20px;
        }
        .admin-auth-card {
            background: var(--bg-color);
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            padding: 2.5rem;
            width: 100%;
            max-width: 420px;
            position: relative;
        }
        .admin-auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .admin-auth-header img {
            width: 120px;
            margin-bottom: 1rem;
        }
        .admin-auth-header h1 {
            color: var(--text-color);
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }
        .admin-auth-header p {
            color: var(--text-secondary);
            font-size: 0.95rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-color);
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: var(--input-bg);
            color: var(--text-color);
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .form-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-color-light);
            outline: none;
        }
        .btn-login {
            width: 100%;
            padding: 0.875rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .btn-login:hover {
            background: var(--primary-color-dark);
        }
        .create-admin-link {
            display: block;
            text-align: center;
            margin-top: 1rem;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        .create-admin-link:hover {
            color: var(--primary-color-dark);
        }
        .error-message {
            background: var(--error-bg);
            color: var(--error-color);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .error-message i {
            font-size: 1.1rem;
        }
    </style>
</head>
<body class="auth-page">
    <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
    <div class="auth-container">
        <div class="admin-auth-card">
            <div class="admin-auth-header">
                <img src="../assets/images/dark-logo.png" alt="MindCare Logo">
                <h1>Admin Login</h1>
                <p>Access the administration panel</p>
            </div>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php 
                        echo htmlspecialchars($_SESSION['error']); 
                        unset($_SESSION['error']);
                    ?>
                </div>
            <?php endif; ?>

            <form action="process_login.php" method="POST" class="auth-form">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required 
                           placeholder="Enter your admin username">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="Enter your password">
                </div>

                <button type="submit" class="btn-login">Login to Admin Panel</button>
            </form>
            <a href="create_admin.php" class="create-admin-link">Create New Admin Account</a>
        </div>
    </div>

    <script src="../assets/js/theme.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Admin Account - MindCare</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <style>
        .admin-auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: var(--bg-gradient);
            padding: 20px;
        }
        .admin-auth-card {
            background: var(--bg-color);
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            padding: 2.5rem;
            width: 100%;
            max-width: 420px;
            position: relative;
        }
        .admin-auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .admin-auth-header h1 {
            color: var(--text-color);
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
        }
        .admin-auth-header p {
            color: var(--text-secondary);
            font-size: 0.95rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-color);
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: var(--input-bg);
            color: var(--text-color);
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .form-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-color-light);
            outline: none;
        }
        .btn-create {
            width: 100%;
            padding: 0.875rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .btn-create:hover {
            background: var(--primary-color-dark);
        }
        .error-message {
            background: var(--error-bg);
            color: var(--error-color);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        .success-message {
            background: var(--success-bg);
            color: var(--success-color);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body class="auth-page">
    <button class="theme-toggle" aria-label="Toggle theme">🌙</button>

    <div class="auth-container">

        <div class="admin-auth-container">
            <div class="admin-auth-card">
                <div class="admin-auth-header">
                    <h1>Create Admin Account</h1>
                    <p>Create a new administrator account</p>
                </div>
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="error-message">
                        <?php 
                        echo $_SESSION['error'];
                        unset($_SESSION['error']);
                        ?>
                    </div>
                <?php endif; ?>
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="success-message">
                        <?php 
                        echo $_SESSION['success'];
                        unset($_SESSION['success']);
                        ?>
                    </div>
                <?php endif; ?>
                <form action="process_create_admin.php" method="POST" id="createAdminForm">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required minlength="4" maxlength="50">
                    </div>
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name" required maxlength="100">
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required maxlength="100">
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required minlength="8">
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">Confirm Password</label>
                        <input type="password" id="confirm_password" name="confirm_password" required minlength="8">
                    </div>
                    <button type="submit" class="btn-create">Create Admin Account</button>
                </form>
                <a href="login.php" class="login-link" style="display: block; text-align: center; margin-top: 1rem; color: var(--primary-color); text-decoration: none;">Back to Login</a>
            </div>
        </div>
        <script>
            document.getElementById('createAdminForm').addEventListener('submit', function(e) {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm_password').value;
                
                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert('Passwords do not match!');
                    return;
                }
                
                
            });
        </script>
        <script src="../assets/js/theme.js"></script>
    </body>
    </html>
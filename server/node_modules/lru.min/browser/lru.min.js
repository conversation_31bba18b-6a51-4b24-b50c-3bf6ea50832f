"use strict";window.createLRU=function(e){var r=e.max;if(!(Number.isInteger(r)&&r>0))throw new TypeError("`max` must be a positive integer");var n=0,i=0,t=0,a=[],o=e.onEviction,l=new Map,f=new Array(r).fill(void 0),u=new Array(r).fill(void 0),v=new Array(r).fill(0),s=new Array(r).fill(0),d=function(e,r){if(e!==t){var n=v[e],a=s[e];e===i?i=n:("get"===r||0!==a)&&(v[a]=n),0!==n&&(s[n]=a),v[t]=e,s[e]=t,v[e]=0,t=e}},p=function(){var e=i,r=f[e];return null==o||o(r,u[e]),l.delete(r),f[e]=void 0,u[e]=void 0,0!==(i=v[e])&&(s[i]=0),0===--n&&(i=t=0),a.push(e),e};return{set:function(e,v){if(void 0!==e){var s=l.get(e);void 0===s?(s=n===r?p():a.length>0?a.pop():n,l.set(e,s),f[s]=e,n++):null==o||o(e,u[s]),u[s]=v,1===n?i=t=s:d(s,"set")}},get:function(e){var r=l.get(e);if(void 0!==r)return r!==t&&d(r,"get"),u[r]},peek:function(e){var r=l.get(e);return void 0!==r?u[r]:void 0},has:function(e){return l.has(e)},keys:function*(){for(var e=t,r=0;r<n;r++)yield f[e],e=s[e]},values:function*(){for(var e=t,r=0;r<n;r++)yield u[e],e=s[e]},entries:function*(){for(var e=t,r=0;r<n;r++)yield[f[e],u[e]],e=s[e]},forEach:function(e){for(var r=t,i=0;i<n;i++){var a=f[r];e(u[r],a),r=s[r]}},delete:function(e){var r=l.get(e);if(void 0===r)return!1;null==o||o(e,u[r]),l.delete(e),a.push(r),f[r]=void 0,u[r]=void 0;var d=s[r],p=v[r];return 0!==d&&(v[d]=p),0!==p&&(s[p]=d),r===i&&(i=p),r===t&&(t=d),n--,!0},evict:function(e){for(var r=Math.min(e,n);r>0;)p(),r--},clear:function(){if("function"==typeof o)for(var e=l.values(),r=e.next();!r.done;r=e.next())o(f[r.value],u[r.value]);l.clear(),f.fill(void 0),u.fill(void 0),a=[],n=0,i=t=0},resize:function(e){if(!(Number.isInteger(e)&&e>0))throw new TypeError("`max` must be a positive integer");if(e!==r){if(e<r){for(var d=t,p=Math.min(n,e),c=n-p,y=new Array(e),g=new Array(e),h=new Array(e),w=new Array(e),A=1;A<=c;A++)null==o||o(f[A],u[A]);for(var m=p-1;m>=0;m--)y[m]=f[d],g[m]=u[d],h[m]=m+1,w[m]=m-1,l.set(y[m],m),d=s[d];i=0,t=p-1,n=p,f.length=e,u.length=e,v.length=e,s.length=e;for(var x=0;x<p;x++)f[x]=y[x],u[x]=g[x],v[x]=h[x],s[x]=w[x];a=[];for(var b=p;b<e;b++)a.push(b)}else{var E=e-r;f.push.apply(f,new Array(E).fill(void 0)),u.push.apply(u,new Array(E).fill(void 0)),v.push.apply(v,new Array(E).fill(0)),s.push.apply(s,new Array(E).fill(0))}r=e}},get max(){return r},get size(){return n},get available(){return r-n}}};
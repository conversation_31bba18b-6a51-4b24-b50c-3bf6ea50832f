<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if therapist is logged in and has approved status
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'therapist') {
    header('Location: ../login.php');
    exit();
}

// Get therapist information
$therapist_id = $_SESSION['user_id'];
$stmt = $pdo->prepare("SELECT * FROM therapist_profiles WHERE user_id = ? AND status = 'approved'");
$stmt->execute([$therapist_id]);
$therapist = $stmt->fetch(PDO::FETCH_ASSOC);

// Get client sessions with notes
$stmt = $pdo->prepare("SELECT s.*, u.first_name, u.last_name
                      FROM sessions s
                      JOIN users u ON s.user_id = u.id
                      WHERE s.therapist_id = ?
                      ORDER BY s.session_date DESC");
$stmt->execute([$therapist_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle form submission for adding/updating notes
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $session_id = $_POST['session_id'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $progress = $_POST['progress'] ?? '';
    $recommendations = $_POST['recommendations'] ?? '';
    $next_steps = $_POST['next_steps'] ?? '';
    
    try {
        if ($session_id) {
            // Update existing session notes
            $stmt = $pdo->prepare("UPDATE sessions SET 
                notes = ?,
                progress = ?,
                recommendations = ?,
                next_steps = ?,
                updated_at = NOW()
                WHERE id = ? AND therapist_id = ?");
            $stmt->execute([$notes, $progress, $recommendations, $next_steps, $session_id, $therapist_id]);
        } else {
            // Create new session notes
            $user_id = $_POST['user_id'];
            $session_date = $_POST['session_date'];
            
            $stmt = $pdo->prepare("INSERT INTO sessions 
                (therapist_id, user_id, session_date, notes, progress, recommendations, next_steps)
                VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$therapist_id, $user_id, $session_date, $notes, $progress, $recommendations, $next_steps]);
        }
        
        $_SESSION['success'] = 'Session notes saved successfully';
        header('Location: session-notes.php');
        exit();
    } catch (PDOException $e) {
        $error = 'An error occurred while saving the session notes';
    }
}

// Get success/error messages
$success = $_SESSION['success'] ?? '';
$error = $_SESSION['error'] ?? '';
unset($_SESSION['success'], $_SESSION['error']);
?>
<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Notes - MindCare</title>
    <link rel="stylesheet" href="../assets/css/dashboard.css">
    <link rel="stylesheet" href="../assets/css/sidebar.css">
    <link rel="stylesheet" href="assets/css/therapist-common.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <button class="theme-toggle" aria-label="Toggle theme">🌙</button>
    
    <div class="dashboard-layout">
        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle" aria-label="Toggle menu">
            <i class="fas fa-bars"></i>
        </button>
        
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="main-content">
            <header class="header">
                <h1>Session Notes Management</h1>
            </header>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if (!empty($error)): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>

            <div class="card">
                <button class="btn btn-primary" onclick="showNewSessionForm()">Add New Session Notes</button>

                <div id="newSessionForm" class="session-form" style="display: none;">
                    <h2>New Session Notes</h2>
                    <form action="session-notes.php" method="POST" class="notes-form">
                        <div class="form-group">
                            <label for="user_id">Client</label>
                            <select name="user_id" required>
                                <?php
                                $stmt = $pdo->prepare("SELECT DISTINCT u.id, u.first_name, u.last_name
                                                      FROM users u
                                                      JOIN appointments a ON u.id = a.user_id
                                                      WHERE a.therapist_id = ?");
                                $stmt->execute([$therapist_id]);
                                $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                
                                foreach ($clients as $client): ?>
                                    <option value="<?php echo $client['id']; ?>">
                                        <?php echo htmlspecialchars($client['first_name'] . ' ' . $client['last_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="session_date">Session Date</label>
                            <input type="date" name="session_date" required>
                        </div>

                        <div class="form-group">
                            <label for="notes">Session Notes</label>
                            <textarea name="notes" rows="4" required placeholder="Detailed notes about the session..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="progress">Progress Assessment</label>
                            <textarea name="progress" rows="3" placeholder="Client's progress since last session..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="recommendations">Recommendations</label>
                            <textarea name="recommendations" rows="3" placeholder="Therapeutic recommendations and exercises..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="next_steps">Next Steps</label>
                            <textarea name="next_steps" rows="3" placeholder="Plan for next session..."></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">Save Notes</button>
                            <button type="button" class="btn btn-secondary" onclick="hideNewSessionForm()">Cancel</button>
                        </div>
                    </form>
                </div>

                <div class="sessions-list">
                    <?php if (!empty($sessions)): ?>
                        <?php foreach ($sessions as $session): ?>
                            <div class="session-card">
                                <div class="session-header">
                                    <h3><?php echo htmlspecialchars($session['first_name'] . ' ' . $session['last_name']); ?></h3>
                                    <span class="session-date">
                                        <?php echo date('F j, Y', strtotime($session['session_date'])); ?>
                                    </span>
                                </div>

                                <div class="session-content">
                                    <div class="notes-section">
                                        <h4>Session Notes</h4>
                                        <p><?php echo nl2br(htmlspecialchars($session['notes'])); ?></p>
                                    </div>

                                    <?php if ($session['progress']): ?>
                                        <div class="notes-section">
                                            <h4>Progress Assessment</h4>
                                            <p><?php echo nl2br(htmlspecialchars($session['progress'])); ?></p>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($session['recommendations']): ?>
                                        <div class="notes-section">
                                            <h4>Recommendations</h4>
                                            <p><?php echo nl2br(htmlspecialchars($session['recommendations'])); ?></p>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($session['next_steps']): ?>
                                        <div class="notes-section">
                                            <h4>Next Steps</h4>
                                            <p><?php echo nl2br(htmlspecialchars($session['next_steps'])); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="session-actions">
                                    <button class="btn btn-primary" onclick="editSession(<?php echo $session['id']; ?>)">Edit Notes</button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="no-data">No session notes found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/theme.js"></script>
    <script>
        function showNewSessionForm() {
            document.getElementById('newSessionForm').style.display = 'block';
        }

        function hideNewSessionForm() {
            document.getElementById('newSessionForm').style.display = 'none';
        }

        function editSession(sessionId) {
            // Implement edit functionality
            // You can show a modal or redirect to an edit page
            console.log('Edit session:', sessionId);
        }
        
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.mobile-menu-toggle').addEventListener('click', () => {
                document.querySelector('.sidebar').classList.toggle('active');
            });
        });
    </script>
</body>
</html>
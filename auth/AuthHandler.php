<?php
class AuthHandler {
    private $pdo;
    private $maxLoginAttempts = 5;
    private $lockoutDuration = 15; // minutes

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function login($identifier, $password, $role = null) {
        // Do not clear all session data to allow multiple independent logins
        
        try {
            if ($role === 'admin') {
                // Admin authentication using admins table
                $stmt = $this->pdo->prepare(
                    "SELECT id, username, password_hash, name as username 
                    FROM admins WHERE username = ?"
                );
                $stmt->execute([$identifier]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$user || !password_verify($password, $user['password_hash'])) {
                    return ['success' => false, 'message' => 'Invalid username or password'];
                }
                
                // Set up admin session with admin-specific variables
                session_regenerate_id(true);
                $_SESSION['admin_id'] = $user['id'];
                $_SESSION['admin_username'] = $user['username'];
                $_SESSION['admin_role'] = 'admin';
                $_SESSION['admin_last_activity'] = time();
                $_SESSION['admin_is_authenticated'] = true;
                
                return [
                    'success' => true,
                    'user_id' => $user['id'],
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'role' => 'admin'
                    ]
                ];
            }
            
            // Regular user authentication
            $identifier = trim(strtolower($identifier));
            $stmt = $this->pdo->prepare(
                "SELECT id, username, password_hash, role, login_attempts, locked_until, email_verified 
                FROM users WHERE email = ?"
            );
            $stmt->execute([$identifier]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                return ['success' => false, 'message' => 'Invalid credentials'];
            }

            // Check if account is locked
            if ($user['locked_until'] !== null && new DateTime($user['locked_until']) > new DateTime()) {
                return ['success' => false, 'message' => 'Account is temporarily locked. Please try again later.'];
            }

            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                $this->handleFailedLogin($user['id']);
                return ['success' => false, 'message' => 'Invalid credentials'];
            }

            // If role is specified, verify it matches
            if ($role !== null && $user['role'] !== $role) {
                return ['success' => false, 'message' => 'Please use the appropriate login page for your role.'];
            }

            // Login successful - set up user session with regeneration
            session_regenerate_id(true);
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['last_activity'] = time();
            $_SESSION['is_authenticated'] = true;
            $this->resetLoginAttempts($user['id']);

            return [
                'success' => true,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'role' => $user['role']
                ]
            ];

        } catch (PDOException $e) {
            error_log('Login error: ' . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred during login'];
        }
    }

    private function handleFailedLogin($userId) {
        try {
            $stmt = $this->pdo->prepare(
                "UPDATE users 
                SET login_attempts = login_attempts + 1,
                    locked_until = CASE 
                        WHEN login_attempts + 1 >= ? THEN DATE_ADD(NOW(), INTERVAL ? MINUTE)
                        ELSE NULL 
                    END
                WHERE id = ?"
            );
            $stmt->execute([$this->maxLoginAttempts, $this->lockoutDuration, $userId]);
        } catch (PDOException $e) {
            error_log('Failed to update login attempts: ' . $e->getMessage());
        }
    }

    private function resetLoginAttempts($userId) {
        try {
            $stmt = $this->pdo->prepare(
                "UPDATE users 
                SET login_attempts = 0,
                    locked_until = NULL,
                    last_login = NOW()
                WHERE id = ?"
            );
            $stmt->execute([$userId]);
        } catch (PDOException $e) {
            error_log('Failed to reset login attempts: ' . $e->getMessage());
        }
    }

    private function setupSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['last_activity'] = time();
    }

    public function logout() {
        session_unset();
        session_destroy();
        return true;
    }

    public function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }

    public function getCurrentUserRole() {
        return isset($_SESSION['role']) ? $_SESSION['role'] : null;
    }
}
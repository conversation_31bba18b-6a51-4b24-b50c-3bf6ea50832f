<?php
require_once '../config/config.php';
require_once '../config/auth_middleware.php';
require_once 'AuthHandler.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Clear only relevant error messages for this login type
if (isset($_SESSION['user_error'])) {
    unset($_SESSION['user_error']);
}

// Check if this specific user type is already logged in
if (isset($_SESSION['user_id']) && isset($_SESSION['role']) && $_SESSION['role'] === 'user') {
    $redirect_url = isset($_SESSION['user_redirect_after_login']) ? $_SESSION['user_redirect_after_login'] : '../user/dashboard.php';
    unset($_SESSION['user_redirect_after_login']);
    header('Location: ' . $redirect_url);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $email = trim(strtolower($_POST['email']));
        $password = $_POST['password'];
        $auth = new AuthHandler($pdo);
        
        $result = $auth->login($email, $password, 'user');
        
        if ($result['success']) {
            // Reset login attempts
            $stmt = $pdo->prepare("UPDATE users SET login_attempts = 0, locked_until = NULL, last_login = NOW() WHERE id = ?");
            $stmt->execute([$result['user_id']]);

            // Log successful login
            $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, ip_address, user_agent, status) VALUES (?, ?, ?, 'success')");
            $stmt->execute([$result['user_id'], $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']]);

            // Set login timestamp and session data with user-specific prefixes
            $_SESSION['user_login_time'] = time();
            $_SESSION['user_id'] = $result['user']['id'];
            $_SESSION['role'] = $result['user']['role'];
            $_SESSION['user_username'] = $result['user']['username'];
            
            // Set the intended redirect URL based on user role
            $redirect_url = '../user/dashboard.php';
            if (isset($_SESSION['user_redirect_after_login'])) {
                $redirect_url = $_SESSION['user_redirect_after_login'];
                unset($_SESSION['user_redirect_after_login']);
            }
            
            // Redirect to appropriate dashboard
            header('Location: ' . $redirect_url);
            exit();
        } else {
            // Failed login attempt
            if (isset($result['user_id'])) {
                $login_attempts = $result['login_attempts'] + 1;
                
                if ($login_attempts >= 5) {
                    // Lock account for 15 minutes
                    $locked_until = date('Y-m-d H:i:s', strtotime('+15 minutes'));
                    $stmt = $pdo->prepare("UPDATE users SET login_attempts = ?, locked_until = ? WHERE id = ?");
                    $stmt->execute([$login_attempts, $locked_until, $result['user_id']]);
                } else {
                    $stmt = $pdo->prepare("UPDATE users SET login_attempts = ? WHERE id = ?");
                    $stmt->execute([$login_attempts, $result['user_id']]);
                }

                // Log failed login
                $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, ip_address, user_agent, status) VALUES (?, ?, ?, 'failed')");
                $stmt->execute([$result['user_id'], $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']]);
            }

            $_SESSION['user_error'] = $result['message'] ?? "Invalid email or password";
            header('Location: ../login.php');
            exit();
        }
    } catch (PDOException $e) {
        error_log($e->getMessage());
        $_SESSION['error'] = "An error occurred. Please try again later.";
        header('Location: ../login.php');
        exit();
    }
} else {
    header('Location: ../login.php');
    exit();
}

<?php
require_once __DIR__ . '/../config/email_config.php';
require_once __DIR__ . '/../vendor/autoload.php';

class EmailNotificationHandler {
    private $mailbox;
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->connectMailbox();
    }

    private function connectMailbox() {
        $mailbox = '{' . SMTP_HOST . ':993/imap/ssl}INBOX';
        $this->mailbox = imap_open($mailbox, SMTP_USERNAME, SMTP_PASSWORD);
        
        if (!$this->mailbox) {
            throw new Exception('Failed to connect to mailbox: ' . imap_last_error());
        }
    }

    public function fetchNewEmails($userId) {
        $emails = imap_search($this->mailbox, 'UNSEEN');
        
        if (!$emails) {
            return;
        }

        foreach ($emails as $emailId) {
            $header = imap_headerinfo($this->mailbox, $emailId);
            $body = $this->getEmailBody($emailId);
            $emailUid = imap_uid($this->mailbox, $emailId);

            // Check if email already exists
            $stmt = $this->pdo->prepare("SELECT id FROM email_notifications WHERE email_uid = ?");
            $stmt->execute([$emailUid]);
            if ($stmt->fetch()) {
                continue;
            }

            // Store email notification
            $stmt = $this->pdo->prepare(
                "INSERT INTO email_notifications 
                (user_id, subject, content, sender, received_date, email_uid) 
                VALUES (?, ?, ?, ?, ?, ?)"
            );
            
            $stmt->execute([
                $userId,
                $header->subject,
                $body,
                $header->from[0]->mailbox . '@' . $header->from[0]->host,
                date('Y-m-d H:i:s', strtotime($header->date)),
                $emailUid
            ]);

            // Create system notification for new email
            $stmt = $this->pdo->prepare(
                "INSERT INTO notifications 
                (user_id, type, title, message, reference_id) 
                VALUES (?, 'email', ?, ?, ?)"
            );

            $stmt->execute([
                $userId,
                'New Email: ' . $header->subject,
                'You have received a new email from ' . $header->from[0]->mailbox . '@' . $header->from[0]->host,
                $this->pdo->lastInsertId()
            ]);
        }
    }

    private function getEmailBody($emailId) {
        $structure = imap_fetchstructure($this->mailbox, $emailId);
        
        if (!isset($structure->parts)) {
            // Single part email
            $body = imap_body($this->mailbox, $emailId);
            return $this->decodeBody($body, $structure->encoding);
        }

        // Multipart email - get HTML or text part
        $body = '';
        foreach ($structure->parts as $partNum => $part) {
            if ($part->subtype == 'HTML' || $part->subtype == 'PLAIN') {
                $body = imap_fetchbody($this->mailbox, $emailId, $partNum + 1);
                $body = $this->decodeBody($body, $part->encoding);
                if ($part->subtype == 'HTML') {
                    break;
                }
            }
        }
        return $body;
    }

    private function decodeBody($body, $encoding) {
        switch ($encoding) {
            case 4: // QUOTED-PRINTABLE
                return quoted_printable_decode($body);
            case 3: // BASE64
                return base64_decode($body);
            default:
                return $body;
        }
    }

    public function __destruct() {
        if ($this->mailbox) {
            imap_close($this->mailbox);
        }
    }
}
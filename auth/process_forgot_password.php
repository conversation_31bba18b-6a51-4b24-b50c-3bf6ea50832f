<?php
require_once '../config/config.php';
session_start();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);

    try {
        // Check if email exists
        $stmt = $pdo->prepare("SELECT id, username FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();

        if ($user) {
            // Generate reset token
            $token = bin2hex(random_bytes(32));
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

            // Store token in database
            $stmt = $pdo->prepare("INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, ?, ?)");
            $stmt->execute([$user['id'], $token, $expires]);

            // In a real application, send email here
            // For demo purposes, we'll just show a success message
            $_SESSION['success'] = "If an account exists with this email, you will receive reset instructions.";
        } else {
            // Don't reveal if email exists or not
            $_SESSION['success'] = "If an account exists with this email, you will receive reset instructions.";
        }

        header('Location: ../forgot-password.php');
        exit();

    } catch (PDOException $e) {
        $_SESSION['error'] = "An error occurred. Please try again later.";
        header('Location: ../forgot-password.php');
        exit();
    }
} 
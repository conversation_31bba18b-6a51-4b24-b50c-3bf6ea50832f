<?php
require_once '../config/database.php';
require_once '../config/email_config.php';
require_once 'EmailHandler.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$error = '';
$success = '';
$showVerificationButton = false;
$unverifiedEmail = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['resend_verification'])) {
        $email = $_POST['email'] ?? '';
        try {
            $stmt = $pdo->prepare("SELECT id, email, verification_token FROM users WHERE email = ? AND email_verified = 0");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            if ($user) {
                $emailHandler = new EmailHandler();
                $emailHandler->sendVerificationEmail($email, $user['verification_token']);
                $success = 'Verification email has been resent. Please check your inbox.';
            } else {
                $error = 'No unverified account found with this email.';
            }
        } catch (Exception $e) {
            error_log('Resend verification error: ' . $e->getMessage());
            $error = 'Failed to resend verification email. Please try again later.';
        }
    } else {
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';

        if (empty($email) || empty($password)) {
            $error = 'Please enter both email and password.';
        } else {
            try {
                $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
                $stmt->execute([$email]);
                $user = $stmt->fetch();

                if ($user && password_verify($password, $user['password_hash'])) {
                    if ($user['role'] === 'therapist' && !$user['email_verified']) {
                        $error = 'Please verify your email address before logging in.';
                        $showVerificationButton = true;
                        $unverifiedEmail = $email;
                    } else {
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['role'] = $user['role'];
                        $_SESSION['username'] = $user['username'];

                        switch ($user['role']) {
                            case 'admin':
                                header('Location: ../admin/dashboard.php');
                                break;
                            case 'therapist':
                                header('Location: ../therapist/dashboard.php');
                                break;
                            case 'client':
                                header('Location: ../user/dashboard.php');
                                break;
                            default:
                                header('Location: ../index.php');
                        }
                        exit();
                    }
                } else {
                    $error = 'Invalid email or password.';
                }
            } catch (PDOException $e) {
                error_log('Login error: ' . $e->getMessage());
                $error = 'An error occurred. Please try again later.';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - MindCare</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body">
                        <h2 class="card-title text-center mb-4">Login</h2>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <?php echo htmlspecialchars($error); ?>
                                <?php if ($showVerificationButton): ?>
                                    <form method="POST" class="mt-2">
                                        <input type="hidden" name="email" value="<?php echo htmlspecialchars($unverifiedEmail); ?>">
                                        <button type="submit" name="resend_verification" class="btn btn-warning btn-sm">
                                            Resend Verification Email
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <?php echo htmlspecialchars($success); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">Login</button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-3">
                            <a href="forgot_password.php">Forgot Password?</a>
                        </div>
                        
                        <div class="text-center mt-3">
                            <p>Don't have an account? <a href="register.php">Register</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 
<?php
require_once '../config/config.php';
require_once '../config/email_config.php';
require_once 'EmailHandler.php';

$error = '';
$success = '';
$loginLink = '';

if (isset($_GET['token'])) {
    $token = urldecode($_GET['token']);
    
    try {
        // Find user with this verification token
        $stmt = $pdo->prepare("SELECT id, email, role FROM users WHERE verification_token = ? AND email_verified = 0");
        $stmt->execute([$token]);
        $user = $stmt->fetch();
        
        if ($user) {
            // Update user as verified
            $updateStmt = $pdo->prepare("UPDATE users SET email_verified = 1, verification_token = NULL WHERE id = ?");
            $updateStmt->execute([$user['id']]);
            
            $success = 'Your email has been verified successfully. You can now log in.';
            
            // Set the appropriate login link based on user role
            if ($user['role'] === 'therapist') {
                $loginLink = '/mindcare/therapist/login.php';
            } else {
                $loginLink = '/mindcare/auth/login.php';
            }
            
            // Send welcome email
            try {
                $emailHandler = new EmailHandler();
                $emailHandler->sendWelcomeEmail($user['email'], $user['role']);
            } catch (Exception $e) {
                error_log('Welcome email error: ' . $e->getMessage());
                // Don't show error to user as verification was successful
            }
        } else {
            $error = 'Invalid or expired verification token.';
        }
    } catch (PDOException $e) {
        error_log('Email verification error: ' . $e->getMessage());
        $error = 'An error occurred during verification. Please try again later.';
    }
} else {
    $error = 'No verification token provided.';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - MindCare</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body text-center">
                        <h2 class="card-title mb-4">Email Verification</h2>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <?php echo htmlspecialchars($success); ?>
                                <div class="mt-3">
                                    <a href="<?php echo htmlspecialchars($loginLink); ?>" class="btn btn-primary">Proceed to Login</a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php
require_once '../config/config.php';
session_start();

if (isset($_GET['token'])) {
    $token = $_GET['token'];
    
    try {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE verification_token = ? AND email_verified = 0");
        $stmt->execute([$token]);
        $user = $stmt->fetch();

        if ($user) {
            $stmt = $pdo->prepare("UPDATE users SET email_verified = 1, verification_token = NULL WHERE id = ?");
            $stmt->execute([$user['id']]);
            
            $_SESSION['success'] = "Email verified successfully! You can now login.";
        } else {
            $_SESSION['error'] = "Invalid or expired verification token.";
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = "An error occurred. Please try again later.";
    }
    
    header('Location: ../login.php');
    exit();
}
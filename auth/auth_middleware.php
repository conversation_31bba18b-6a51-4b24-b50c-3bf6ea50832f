<?php
function checkAuth() {
    // Check if user is logged in with proper session variables
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
        // Clear any existing session data
        session_unset();
        session_destroy();
        session_start();
        
        header('Location: ../login.php');
        exit();
    }

    // Redirect based on role if accessing wrong area
    $current_path = $_SERVER['PHP_SELF'];
    $role = $_SESSION['role'];

    if (strpos($current_path, '/admin/') !== false && $role !== 'admin') {
        session_unset();
        session_destroy();
        session_start();
        header('Location: ../login.php');
        exit();
    }
    if (strpos($current_path, '/therapist/') !== false && $role !== 'therapist') {
        session_unset();
        session_destroy();
        session_start();
        header('Location: ../therapist/login.php');
        exit();
    }
    if (strpos($current_path, '/user/') !== false && $role !== 'user') {
        session_unset();
        session_destroy();
        session_start();
        header('Location: ../login.php');
        exit();
    }
}

function checkAdmin() {
    if (!isset($_SESSION['admin_id']) || $_SESSION['role'] !== 'admin') {
        header('Location: ../admin/login.php');
        exit();
    }
}

function checkTherapist() {
    if (!isset($_SESSION['therapist_id']) || $_SESSION['role'] !== 'therapist') {
        header('Location: ../therapist/login.php');
        exit();
    }
}

function isGuest() {
    return isset($_SESSION['is_guest']) && $_SESSION['is_guest'];
}

function clearAllSessions() {
    unset($_SESSION['user_id']);
    unset($_SESSION['admin_id']);
    unset($_SESSION['therapist_id']);
    unset($_SESSION['role']);
    unset($_SESSION['username']);
    unset($_SESSION['is_authenticated']);
    unset($_SESSION['last_activity']);
    unset($_SESSION['is_guest']);
}

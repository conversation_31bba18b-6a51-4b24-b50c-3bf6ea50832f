<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
require_once 'EmailHandler.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username']);
    $email = trim(strtolower($_POST['email']));
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    $errors = [];
    
    if (strlen($username) < 3 || strlen($username) > 50) {
        $errors[] = "Username must be between 3 and 50 characters";
    }
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    if (strlen($password) < 8) {
        $errors[] = "Password must be at least 8 characters long";
    }
    if (!preg_match("/[A-Z]/", $password)) {
        $errors[] = "Password must contain at least one uppercase letter";
    }
    if (!preg_match("/[0-9]/", $password)) {
        $errors[] = "Password must contain at least one number";
    }
    if ($password !== $confirm_password) {
        $errors[] = "Passwords do not match";
    }

    if (!empty($errors)) {
        $_SESSION['errors'] = $errors;
        $_SESSION['form_data'] = ['username' => $username, 'email' => $email];
        header('Location: ../register.php');
        exit();
    }

    try {
        $pdo->beginTransaction();

        // Check existing username
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$username]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception("Username already exists");
        }

        // Check existing email
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception("Email already registered");
        }

        // Generate verification token
        $verification_token = bin2hex(random_bytes(32));
        error_log("Generated verification token for {$email}: {$verification_token}");
        
        // Hash password
        $password_hash = password_hash($password, PASSWORD_DEFAULT);

        // Insert new user
        $stmt = $pdo->prepare("
            INSERT INTO users (
                username, 
                email, 
                password_hash, 
                verification_token,
                email_verified,
                role,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");

        $email_verified = 0; // Set to 0 for unverified
        $role = 'user'; // Default role

        $stmt->execute([
            $username,
            $email,
            $password_hash,
            $verification_token,
            $email_verified,
            $role
        ]);

        $user_id = $pdo->lastInsertId();

        // For development/testing, you can auto-verify the email
        // Remove or comment this in production
        /*
        $stmt = $pdo->prepare("UPDATE users SET email_verified = 1 WHERE id = ?");
        $stmt->execute([$user_id]);
        */

        $pdo->commit();

        // Send verification email
        try {
            $emailHandler = new EmailHandler();
            $emailHandler->sendVerificationEmail($email, $verification_token);
            $_SESSION['success'] = "Registration successful! Please check your email to verify your account.";
        } catch (Exception $e) {
            error_log('Failed to send verification email: ' . $e->getMessage());
            $_SESSION['success'] = "Registration successful! However, we couldn't send the verification email. Please contact support.";
        }
        
        // Redirect to login page
        header('Location: ../login.php');
        exit();

    } catch (Exception $e) {
        $pdo->rollBack();
        $_SESSION['error'] = $e->getMessage();
        $_SESSION['form_data'] = ['username' => $username, 'email' => $email];
        header('Location: ../register.php');
        exit();
    }
}

// If not POST request, redirect to registration page
header('Location: ../register.php');
exit();

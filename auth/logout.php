<?php
require_once '../config/config.php';
require_once '../config/no_cache.php';
session_start();

try {
    // Determine user type and clear only relevant session variables
    if (isset($_SESSION['role'])) {
        $user_id = null;
        $user_type = $_SESSION['role'];

        switch ($user_type) {
            case 'admin':
                if (isset($_SESSION['user_id'])) {
                    $user_id = $_SESSION['user_id'];
                }
                unset($_SESSION['user_id']);
                unset($_SESSION['role']);
                unset($_SESSION['admin_login_time']);
                $redirect = '../admin/login.php';
                break;

            case 'therapist':
                if (isset($_SESSION['therapist_user_id'])) {
                    $user_id = $_SESSION['therapist_user_id'];
                }
                unset($_SESSION['therapist_user_id']);
                unset($_SESSION['therapist_role']);
                unset($_SESSION['therapist_id']);
                unset($_SESSION['therapist_login_time']);
                $redirect = '../therapist/login.php';
                break;

            default: // regular user
                if (isset($_SESSION['user_id'])) {
                    $user_id = $_SESSION['user_id'];
                }
                unset($_SESSION['user_id']);
                unset($_SESSION['role']);
                unset($_SESSION['login_time']);
                unset($_SESSION['is_guest']);
                $redirect = '../login.php';
                break;
        }

        // Log the logout event if user was logged in
        if ($user_id && !isset($_SESSION['is_guest'])) {
            $stmt = $pdo->prepare("INSERT INTO login_logs (user_id, action, status) VALUES (?, 'logout', 'success')");
            $stmt->execute([$user_id]);
        }
    } else {
        // If no role is set, redirect to main login
        $redirect = '../login.php';
    }

    // Prevent caching of the redirect
    header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
    header("Cache-Control: post-check=0, pre-check=0", false);
    header("Pragma: no-cache");
    
    // Redirect to appropriate login page
    header('Location: ' . $redirect);
    exit();

} catch (Exception $e) {
    // Log the error (in a production environment)
    error_log("Logout error: " . $e->getMessage());
    
    // Redirect to main login page with error parameter
    header('Location: ../login.php?error=logout');
    exit();
}
?>

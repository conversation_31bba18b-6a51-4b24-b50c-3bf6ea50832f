<?php
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require_once '../vendor/autoload.php';
require_once '../config/email_config.php';

class EmailHandler {
    private $mailer;
    
    public function __construct() {
        $this->mailer = new PHPMailer(true);
        $this->setupMailer();
    }
    
    private function setupMailer() {
        try {
            $this->mailer->isSMTP();
            $this->mailer->Host = SMTP_HOST;
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = SMTP_USERNAME;
            $this->mailer->Password = SMTP_PASSWORD;
            $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $this->mailer->Port = SMTP_PORT;
            $this->mailer->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        } catch (Exception $e) {
            error_log('Mailer setup error: ' . $e->getMessage());
            throw new Exception('Failed to setup email configuration');
        }
    }
    
    public function sendVerificationEmail($email, $token) {
        try {
            $this->mailer->addAddress($email);
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Verify Your Email - MindCare';
            
            $verificationLink = "http://" . $_SERVER['HTTP_HOST'] . "/mindcare/auth/verify_email.php?token=" . urlencode($token);
            
            $body = "
                <h2>Welcome to MindCare!</h2>
                <p>Please verify your email address by clicking the link below:</p>
                <p><a href='{$verificationLink}'>{$verificationLink}</a></p>
                <p>If you did not create an account, please ignore this email.</p>
                <p>Best regards,<br>MindCare Team</p>
            ";
            
            $this->mailer->Body = $body;
            $this->mailer->send();
            return true;
        } catch (Exception $e) {
            error_log('Verification email error: ' . $e->getMessage());
            throw new Exception('Failed to send verification email');
        }
    }
    
    public function sendWelcomeEmail($email, $role) {
        try {
            $this->mailer->addAddress($email);
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Welcome to MindCare!';
            
            $body = "
                <h2>Welcome to MindCare!</h2>
                <p>Thank you for verifying your email address.</p>
                <p>You are now registered as a " . ucfirst($role) . ".</p>
                <p>You can now log in to your account and start using our services.</p>
                <p>Best regards,<br>MindCare Team</p>
            ";
            
            $this->mailer->Body = $body;
            $this->mailer->send();
            return true;
        } catch (Exception $e) {
            error_log('Welcome email error: ' . $e->getMessage());
            throw new Exception('Failed to send welcome email');
        }
    }
    
    public function sendApprovalEmail($email, $status, $reason = '') {
        try {
            $this->mailer->addAddress($email);
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Therapist Application ' . ucfirst($status) . ' - MindCare';
            
            $body = "
                <h2>Therapist Application Update</h2>
                <p>Your therapist application has been <strong>" . strtolower($status) . "</strong>.</p>
            ";
            
            if ($status === 'approved') {
                $body .= "
                    <p>Congratulations! You can now log in to your therapist dashboard and start helping clients.</p>
                    <p><a href='http://" . $_SERVER['HTTP_HOST'] . "/therapist/dashboard.php'>Access Your Dashboard</a></p>
                ";
            } else {
                $body .= "
                    <p>Reason: " . htmlspecialchars($reason) . "</p>
                    <p>If you believe this is a mistake, please contact our support team.</p>
                ";
            }
            
            $body .= "
                <p>Best regards,<br>MindCare Team</p>
            ";
            
            $this->mailer->Body = $body;
            $this->mailer->send();
            return true;
        } catch (Exception $e) {
            error_log('Approval email error: ' . $e->getMessage());
            throw new Exception('Failed to send approval email');
        }
    }
    
    // New method for sending therapist approval email
    public function sendTherapistApprovalEmail($email, $name) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Your Therapist Application Has Been Approved - MindCare';
            
            $body = "
                <h2>Congratulations, {$name}!</h2>
                <p>We are pleased to inform you that your application to become a therapist on MindCare has been <strong>approved</strong>.</p>
                <p>You can now log in to your therapist dashboard and start helping clients.</p>
                <p><a href='http://" . $_SERVER['HTTP_HOST'] . "/mindcare/therapist/dashboard.php'>Access Your Therapist Dashboard</a></p>
                <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                <p>Best regards,<br>MindCare Team</p>
            ";
            
            $this->mailer->Body = $body;
            $this->mailer->send();
            return true;
        } catch (Exception $e) {
            error_log('Therapist approval email error: ' . $e->getMessage());
            throw new Exception('Failed to send therapist approval email');
        }
    }
    
    // Method for sending therapist rejection email
    public function sendTherapistRejectionEmail($email, $name) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Your Therapist Application Status - MindCare';
            
            $body = "
                <h2>Hello {$name},</h2>
                <p>We regret to inform you that your application to become a therapist on MindCare has been <strong>rejected</strong>.</p>
                <p>This decision was made after careful review of your application materials.</p>
                <p>If you believe this is a mistake or would like to understand more about the decision, please contact our support team.</p>
                <p>Best regards,<br>MindCare Team</p>
            ";
            
            $this->mailer->Body = $body;
            $this->mailer->send();
            return true;
        } catch (Exception $e) {
            error_log('Therapist rejection email error: ' . $e->getMessage());
            throw new Exception('Failed to send therapist rejection email');
        }
    }
    
    // Method for sending therapist review email
    public function sendTherapistReviewEmail($email, $name) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Your Therapist Application is Under Review - MindCare';
            
            $body = "
                <h2>Hello {$name},</h2>
                <p>Your application to become a therapist on MindCare has been placed under <strong>review</strong>.</p>
                <p>Our team is carefully examining your application materials, and we will notify you of the final decision soon.</p>
                <p>If you have any questions, please contact our support team.</p>
                <p>Best regards,<br>MindCare Team</p>
            ";
            
            $this->mailer->Body = $body;
            $this->mailer->send();
            return true;
        } catch (Exception $e) {
            error_log('Therapist review email error: ' . $e->getMessage());
            throw new Exception('Failed to send therapist review email');
        }
    }
}
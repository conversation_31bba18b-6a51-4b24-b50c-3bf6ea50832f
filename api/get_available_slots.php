<?php
require_once '../config/config.php';
require_once '../auth/auth_middleware.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Validate input parameters
$therapist_id = filter_input(INPUT_GET, 'therapist_id', FILTER_VALIDATE_INT);
$date = filter_input(INPUT_GET, 'date', FILTER_SANITIZE_STRING);

if (!$therapist_id || !$date || !strtotime($date)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid parameters']);
    exit;
}

try {
    // Get therapist's working hours (9 AM to 5 PM by default)
    $start_hour = 9;
    $end_hour = 17;
    $slot_duration = 60; // 60 minutes per session

    // Get existing appointments for the selected date
    $stmt = $pdo->prepare("
        SELECT TIME_FORMAT(appointment_date, '%H:%i') as booked_time
        FROM appointments
        WHERE therapist_id = ?
        AND DATE(appointment_date) = ?
        AND status != 'cancelled'
    ");
    $stmt->execute([$therapist_id, $date]);
    $booked_slots = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Generate available time slots
    $available_slots = [];
    $current_time = strtotime("$date $start_hour:00:00");
    $end_time = strtotime("$date $end_hour:00:00");

    // If the date is today, only show future time slots
    $today = date('Y-m-d') === $date;
    $current_hour = $today ? date('H') : 0;

    while ($current_time < $end_time) {
        $slot_time = date('H:i', $current_time);
        
        // Skip past time slots if it's today
        if ($today && date('H', $current_time) <= $current_hour) {
            $current_time += $slot_duration * 60;
            continue;
        }

        // Check if slot is available
        if (!in_array($slot_time, $booked_slots)) {
            $available_slots[] = date('g:i A', $current_time);
        }

        $current_time += $slot_duration * 60;
    }

    echo json_encode(['success' => true, 'data' => $available_slots]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error fetching available slots']);
    error_log('Error in get_available_slots.php: ' . $e->getMessage());
}
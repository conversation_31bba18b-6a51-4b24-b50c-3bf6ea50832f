<?php
require_once '../config/config.php';
require_once '../auth/auth_middleware.php';

header('Content-Type: application/json');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

checkAuth();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

$user_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['entry_id']) || !is_numeric($data['entry_id'])) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Invalid entry ID']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Verify the entry belongs to the user
    $stmt = $pdo->prepare("SELECT id FROM journal_entries WHERE id = ? AND user_id = ?");
    $stmt->execute([$data['entry_id'], $user_id]);
    
    if (!$stmt->fetch()) {
        $pdo->rollBack();
        http_response_code(403);
        echo json_encode(['status' => 'error', 'message' => 'Unauthorized access']);
        exit;
    }

    // Delete associated mood entry first (foreign key will handle this automatically)
    $stmt = $pdo->prepare("DELETE FROM mood_entries WHERE journal_entry_id = ?");
    $stmt->execute([$data['entry_id']]);

    // Delete the journal entry
    $stmt = $pdo->prepare("DELETE FROM journal_entries WHERE id = ? AND user_id = ?");
    $stmt->execute([$data['entry_id'], $user_id]);

    $pdo->commit();

    echo json_encode([
        'status' => 'success',
        'message' => 'Journal entry deleted successfully'
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to delete journal entry'
    ]);
}
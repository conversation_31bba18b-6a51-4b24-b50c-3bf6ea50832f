<?php
require_once '../config/config.php';
require_once '../api/notification_handler.php';

class AppointmentReminder {
    private $pdo;
    private $notificationHandler;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->notificationHandler = new NotificationHandler($pdo);
    }

    public function scheduleReminders($appointmentId) {
        try {
            // Get appointment details
            $stmt = $this->pdo->prepare("
                SELECT a.*, u.email, u.first_name, u.last_name,
                       t.first_name as therapist_first_name, t.last_name as therapist_last_name
                FROM appointments a
                JOIN users u ON a.user_id = u.id
                JOIN users t ON a.therapist_id = t.id
                WHERE a.id = ?
            ");
            $stmt->execute([$appointmentId]);
            $appointment = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$appointment) {
                return false;
            }

            // Schedule reminders
            $reminderTimes = [
                '24_hours' => date('Y-m-d H:i:s', strtotime($appointment['appointment_date'] . ' -24 hours')),
                '1_hour' => date('Y-m-d H:i:s', strtotime($appointment['appointment_date'] . ' -1 hour')),
                '15_minutes' => date('Y-m-d H:i:s', strtotime($appointment['appointment_date'] . ' -15 minutes'))
            ];

            foreach ($reminderTimes as $type => $reminderTime) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO appointment_reminders 
                    (appointment_id, user_id, reminder_type, reminder_time)
                    VALUES (?, ?, 'both', ?)
                ");
                $stmt->execute([$appointmentId, $appointment['user_id'], $reminderTime]);
            }

            return true;
        } catch (PDOException $e) {
            error_log('Error in scheduleReminders: ' . $e->getMessage());
            return false;
        }
    }

    public function processReminders() {
        try {
            // Get due reminders
            $stmt = $this->pdo->prepare("
                SELECT r.*, a.appointment_date, u.email, u.first_name, u.last_name,
                       t.first_name as therapist_first_name, t.last_name as therapist_last_name
                FROM appointment_reminders r
                JOIN appointments a ON r.appointment_id = a.id
                JOIN users u ON r.user_id = u.id
                JOIN users t ON a.therapist_id = t.id
                WHERE r.reminder_time <= NOW()
                AND r.is_sent = FALSE
                AND a.status = 'scheduled'
            ");
            $stmt->execute();
            $reminders = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($reminders as $reminder) {
                $this->sendReminder($reminder);
            }

            return true;
        } catch (PDOException $e) {
            error_log('Error in processReminders: ' . $e->getMessage());
            return false;
        }
    }

    private function sendReminder($reminder) {
        try {
            $timeUntilAppointment = $this->getTimeUntilAppointment($reminder['appointment_date']);
            $message = $this->generateReminderMessage($reminder, $timeUntilAppointment);

            // Send email
            if (in_array($reminder['reminder_type'], ['email', 'both'])) {
                $this->sendEmailReminder($reminder, $message);
            }

            // Send SMS (implement your SMS gateway here)
            if (in_array($reminder['reminder_type'], ['sms', 'both'])) {
                $this->sendSMSReminder($reminder, $message);
            }

            // Create in-app notification
            $this->notificationHandler->createNotification(
                $reminder['user_id'],
                'appointment_reminder',
                $message,
                $reminder['appointment_id']
            );

            // Mark reminder as sent
            $stmt = $this->pdo->prepare("
                UPDATE appointment_reminders 
                SET is_sent = TRUE, sent_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$reminder['id']]);

            return true;
        } catch (Exception $e) {
            error_log('Error in sendReminder: ' . $e->getMessage());
            return false;
        }
    }

    private function getTimeUntilAppointment($appointmentDate) {
        $now = new DateTime();
        $appointment = new DateTime($appointmentDate);
        $interval = $now->diff($appointment);

        if ($interval->d > 0) {
            return $interval->d . ' day' . ($interval->d > 1 ? 's' : '');
        } elseif ($interval->h > 0) {
            return $interval->h . ' hour' . ($interval->h > 1 ? 's' : '');
        } else {
            return $interval->i . ' minute' . ($interval->i > 1 ? 's' : '');
        }
    }

    private function generateReminderMessage($reminder, $timeUntil) {
        return "Reminder: Your appointment with Dr. {$reminder['therapist_first_name']} {$reminder['therapist_last_name']} " .
               "is in {$timeUntil}. Date: " . date('F j, Y g:i A', strtotime($reminder['appointment_date']));
    }

    private function sendEmailReminder($reminder, $message) {
        $subject = "Appointment Reminder - MindCare";
        $headers = "From: MindCare <<EMAIL>>\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";

        $emailBody = "
            <html>
            <body>
                <h2>Appointment Reminder</h2>
                <p>{$message}</p>
                <p>Please log in to your account to view appointment details or make any changes.</p>
                <p>Best regards,<br>MindCare Team</p>
            </body>
            </html>
        ";

        mail($reminder['email'], $subject, $emailBody, $headers);
    }

    private function sendSMSReminder($reminder, $message) {
        // Implement your SMS gateway integration here
        // Example using a hypothetical SMS service:
        /*
        $smsService = new SMSService();
        $smsService->send($reminder['phone'], $message);
        */
    }
}

// Process reminders (this would typically be run by a cron job)
if (isset($_GET['process']) && $_GET['process'] === 'true') {
    $reminder = new AppointmentReminder($pdo);
    $reminder->processReminders();
} 
<?php
require_once '../config/config.php';
require_once '../includes/helpers.php';
require_once '../auth/auth_middleware.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
checkAuth();

header('Content-Type: application/json');

$user_id = $_SESSION['user_id'];
$response = ['success' => false, 'message' => '', 'data' => null];

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['chat_id'])) {
        $chat_id = $_GET['chat_id'];
        
        // Verify user has access to this chat
        $stmt = $pdo->prepare("SELECT id FROM chats WHERE id = ? AND (user_id = ? OR therapist_id = ?)");
        $stmt->execute([$chat_id, $user_id, $user_id]);
        if (!$stmt->fetch()) {
            http_response_code(403);
            $response['message'] = 'Access denied';
            echo json_encode($response);
            exit;
        }

        // Fetch messages
        $stmt = $pdo->prepare("
            SELECT 
                m.*,
                CONCAT(u.first_name, ' ', u.last_name) as sender_name,
                u.profile_image as sender_avatar
            FROM messages m
            JOIN users u ON m.sender_id = u.id
            WHERE m.chat_id = ?
            ORDER BY m.sent_time ASC
            LIMIT 50
        ");
        $stmt->execute([$chat_id]);
        $messages = $stmt->fetchAll();

        // Mark messages as read
        $stmt = $pdo->prepare("UPDATE messages SET is_read = 1 WHERE chat_id = ? AND recipient_id = ?");
        $stmt->execute([$chat_id, $user_id]);

        $response['success'] = true;
        $response['data'] = $messages;
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (isset($data['chat_id']) && isset($data['content'])) {
        $chat_id = $data['chat_id'];
        $content = trim($data['content']);

        if (empty($content)) {
            $response['message'] = 'Message content cannot be empty';
            echo json_encode($response);
            exit;
        }

        // Get recipient ID
        $stmt = $pdo->prepare("SELECT user_id, therapist_id FROM chats WHERE id = ?");
        $stmt->execute([$chat_id]);
        $chat = $stmt->fetch();

        if (!$chat) {
            $response['message'] = 'Chat not found';
            echo json_encode($response);
            exit;
        }

        $recipient_id = ($chat['user_id'] == $user_id) ? $chat['therapist_id'] : $chat['user_id'];

        // Save message
        $stmt = $pdo->prepare("
            INSERT INTO messages (chat_id, sender_id, recipient_id, content, sent_time)
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        if ($stmt->execute([$chat_id, $user_id, $recipient_id, $content])) {
            // Update last message in chat
            $stmt = $pdo->prepare("
                UPDATE chats 
                SET last_message = ?, 
                    last_message_time = NOW(),
                    unread_count = unread_count + 1
                WHERE id = ?
            ");
            $stmt->execute([$content, $chat_id]);

            $response['success'] = true;
            $response['data'] = [
                'message_id' => $pdo->lastInsertId(),
                'sent_time' => date('Y-m-d H:i:s'),
                'content' => $content
            ];
        } else {
            $response['message'] = 'Failed to send message';
        }
    } else {
        $response['message'] = 'Invalid request data';
    }
}

echo json_encode($response);
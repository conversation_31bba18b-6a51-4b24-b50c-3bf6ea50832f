<?php
require_once '../config/config.php';
require_once '../includes/helpers.php';
require_once '../auth/auth_middleware.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
checkAuth();

// Get request method and path
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path = trim(str_replace('/api/resources', '', $path), '/');

// Get resource ID from path if present
$resourceId = null;
if (preg_match('/^(\d+)(\/tags)?$/', $path, $matches)) {
    $resourceId = $matches[1];
    $isTagsEndpoint = isset($matches[2]);
}

// Handle different HTTP methods
switch ($method) {
    case 'GET':
        if ($resourceId) {
            if ($isTagsEndpoint) {
                getResourceTags($resourceId);
            } else {
                getResource($resourceId);
            }
        } else {
            getResources();
        }
        break;
        
    case 'POST':
        createResource();
        break;
        
    case 'PUT':
        if ($resourceId) {
            updateResource($resourceId);
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Resource ID required']);
        }
        break;
        
    case 'DELETE':
        if ($resourceId) {
            deleteResource($resourceId);
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Resource ID required']);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

function getResources() {
    global $pdo;
    
    try {
        $stmt = $pdo->query("
            SELECT r.*, 
                   u.first_name, 
                   u.last_name,
                   COUNT(DISTINCT rv.id) as view_count,
                   COUNT(DISTINCT rd.id) as download_count
            FROM resources r
            LEFT JOIN users u ON r.created_by = u.id
            LEFT JOIN resource_views rv ON r.id = rv.resource_id
            LEFT JOIN resource_downloads rd ON r.id = rd.resource_id
            WHERE r.status = 'published'
            GROUP BY r.id
            ORDER BY r.created_at DESC
        ");
        
        $resources = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Add tags to each resource
        foreach ($resources as &$resource) {
            $resource['tags'] = getResourceTags($resource['id'], true);
        }
        
        echo json_encode($resources);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch resources']);
    }
}

function getResource($id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT r.*, 
                   u.first_name, 
                   u.last_name,
                   COUNT(DISTINCT rv.id) as view_count,
                   COUNT(DISTINCT rd.id) as download_count
            FROM resources r
            LEFT JOIN users u ON r.created_by = u.id
            LEFT JOIN resource_views rv ON r.id = rv.resource_id
            LEFT JOIN resource_downloads rd ON r.id = rd.resource_id
            WHERE r.id = ?
            GROUP BY r.id
        ");
        
        $stmt->execute([$id]);
        $resource = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$resource) {
            http_response_code(404);
            echo json_encode(['error' => 'Resource not found']);
            return;
        }
        
        // Add tags
        $resource['tags'] = getResourceTags($id, true);
        
        // Record view
        recordResourceView($id);
        
        echo json_encode($resource);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch resource']);
    }
}

function getResourceTags($id, $returnArray = false) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT tag_name FROM resource_tags WHERE resource_id = ?");
        $stmt->execute([$id]);
        $tags = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if ($returnArray) {
            return $tags;
        }
        
        echo json_encode($tags);
        
    } catch (PDOException $e) {
        if ($returnArray) {
            return [];
        }
        
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch resource tags']);
    }
}

function createResource() {
    global $pdo;
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Handle file uploads
        $imageUrl = null;
        $fileUrl = null;
        
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $imageUrl = handleFileUpload($_FILES['image'], 'images');
        }
        
        if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
            $fileUrl = handleFileUpload($_FILES['file'], 'files');
        }
        
        // Insert resource
        $stmt = $pdo->prepare("
            INSERT INTO resources (
                title, description, content, category, icon, 
                image_url, file_url, duration, created_by, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $_POST['title'],
            $_POST['description'],
            $_POST['content'],
            $_POST['category'],
            $_POST['icon'],
            $imageUrl,
            $fileUrl,
            $_POST['duration'],
            $_SESSION['user_id'],
            'published'
        ]);
        
        $resourceId = $pdo->lastInsertId();
        
        // Handle tags
        if (!empty($_POST['tags'])) {
            $tags = array_map('trim', explode(',', $_POST['tags']));
            $stmt = $pdo->prepare("INSERT INTO resource_tags (resource_id, tag_name) VALUES (?, ?)");
            
            foreach ($tags as $tag) {
                $stmt->execute([$resourceId, $tag]);
            }
        }
        
        // Commit transaction
        $pdo->commit();
        
        // Return created resource
        getResource($resourceId);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create resource']);
    }
}

function updateResource($id) {
    global $pdo;
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Check if resource exists and belongs to user
        $stmt = $pdo->prepare("SELECT * FROM resources WHERE id = ? AND created_by = ?");
        $stmt->execute([$id, $_SESSION['user_id']]);
        
        if (!$stmt->fetch()) {
            http_response_code(403);
            echo json_encode(['error' => 'Unauthorized to update this resource']);
            return;
        }
        
        // Handle file uploads
        $imageUrl = null;
        $fileUrl = null;
        
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $imageUrl = handleFileUpload($_FILES['image'], 'images');
        }
        
        if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
            $fileUrl = handleFileUpload($_FILES['file'], 'files');
        }
        
        // Update resource
        $stmt = $pdo->prepare("
            UPDATE resources SET
                title = ?,
                description = ?,
                content = ?,
                category = ?,
                icon = ?,
                duration = ?
                " . ($imageUrl ? ", image_url = ?" : "") . "
                " . ($fileUrl ? ", file_url = ?" : "") . "
            WHERE id = ?
        ");
        
        $params = [
            $_POST['title'],
            $_POST['description'],
            $_POST['content'],
            $_POST['category'],
            $_POST['icon'],
            $_POST['duration']
        ];
        
        if ($imageUrl) {
            $params[] = $imageUrl;
        }
        
        if ($fileUrl) {
            $params[] = $fileUrl;
        }
        
        $params[] = $id;
        
        $stmt->execute($params);
        
        // Update tags
        $stmt = $pdo->prepare("DELETE FROM resource_tags WHERE resource_id = ?");
        $stmt->execute([$id]);
        
        if (!empty($_POST['tags'])) {
            $tags = array_map('trim', explode(',', $_POST['tags']));
            $stmt = $pdo->prepare("INSERT INTO resource_tags (resource_id, tag_name) VALUES (?, ?)");
            
            foreach ($tags as $tag) {
                $stmt->execute([$id, $tag]);
            }
        }
        
        // Commit transaction
        $pdo->commit();
        
        // Return updated resource
        getResource($id);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update resource']);
    }
}

function deleteResource($id) {
    global $pdo;
    
    try {
        // Check if resource exists and belongs to user
        $stmt = $pdo->prepare("SELECT * FROM resources WHERE id = ? AND created_by = ?");
        $stmt->execute([$id, $_SESSION['user_id']]);
        
        if (!$stmt->fetch()) {
            http_response_code(403);
            echo json_encode(['error' => 'Unauthorized to delete this resource']);
            return;
        }
        
        // Delete resource (cascade will handle related records)
        $stmt = $pdo->prepare("DELETE FROM resources WHERE id = ?");
        $stmt->execute([$id]);
        
        echo json_encode(['success' => true]);
        
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete resource']);
    }
}

function recordResourceView($id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO resource_views (resource_id, user_id)
            VALUES (?, ?)
        ");
        
        $stmt->execute([$id, $_SESSION['user_id']]);
        
    } catch (PDOException $e) {
        // Log error but don't fail the request
        error_log("Failed to record resource view: " . $e->getMessage());
    }
}

function handleFileUpload($file, $type) {
    $uploadDir = "../uploads/resources/{$type}/";
    
    // Create directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return "/uploads/resources/{$type}/" . $filename;
    }
    
    throw new Exception("Failed to upload file");
} 
<?php
require_once '../config/config.php';
require_once '../includes/helpers.php';
require_once '../auth/auth_middleware.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
checkAuth();

header('Content-Type: application/json');

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method';
    echo json_encode($response);
    exit();
}

if (!isset($_FILES['avatar'])) {
    $response['message'] = 'No file uploaded';
    echo json_encode($response);
    exit();
}

$file = $_FILES['avatar'];
$user_id = $_SESSION['user_id'];

// Validate file
$allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
$max_size = 5 * 1024 * 1024; // 5MB

if (!in_array($file['type'], $allowed_types)) {
    $response['message'] = 'Invalid file type. Only JPG, PNG and GIF files are allowed';
    echo json_encode($response);
    exit();
}

if ($file['size'] > $max_size) {
    $response['message'] = 'File is too large. Maximum size is 5MB';
    echo json_encode($response);
    exit();
}

// Create upload directory if it doesn't exist
$upload_dir = '../uploads/avatars/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// Generate unique filename
$extension = pathinfo($file['name'], PATHINFO_EXTENSION);
$filename = 'avatar_' . $user_id . '_' . time() . '.' . $extension;
$filepath = $upload_dir . $filename;

// Move uploaded file
if (move_uploaded_file($file['tmp_name'], $filepath)) {
    // Update user's profile image in database
    $relative_path = '../uploads/avatars/' . $filename;
    $stmt = $pdo->prepare("UPDATE users SET profile_image = ? WHERE id = ?");
    
    if ($stmt->execute([$relative_path, $user_id])) {
        // Delete old avatar if exists
        $stmt = $pdo->prepare("SELECT profile_image FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $old_image = $stmt->fetchColumn();
        
        if ($old_image && $old_image !== '../assets/images/default-image.jpg' && file_exists($old_image)) {
            unlink($old_image);
        }
        
        $response['success'] = true;
        $response['message'] = 'Profile picture updated successfully';
    } else {
        unlink($filepath); // Delete uploaded file if database update fails
        $response['message'] = 'Failed to update profile picture in database';
    }
} else {
    $response['message'] = 'Failed to upload file';
}

echo json_encode($response);
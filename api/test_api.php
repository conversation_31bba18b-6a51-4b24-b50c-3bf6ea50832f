<?php
// Enable error display for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

header('Content-Type: text/html');

echo "<h1>API Testing Script</h1>";

echo "<h2>Step 1: Include config files</h2>";
try {
    require_once '../config/config.php';
    require_once '../includes/helpers.php';
    echo "<p style='color:green'>✓ Config files included successfully</p>";
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Error: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>Step 2: Check database connection</h2>";
try {
    if (!isset($pdo)) {
        throw new Exception('$pdo variable not set');
    }
    
    // Test the connection
    $pdo->getAttribute(PDO::ATTR_CONNECTION_STATUS);
    echo "<p style='color:green'>✓ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Error: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>Step 3: Check tables existence</h2>";
try {
    // Check users table
    $userTableExists = $pdo->query("SHOW TABLES LIKE 'users'")->rowCount() > 0;
    echo $userTableExists 
        ? "<p style='color:green'>✓ Users table exists</p>" 
        : "<p style='color:red'>✗ Users table does not exist</p>";
    
    // Check therapist_profiles table
    $profilesTableExists = $pdo->query("SHOW TABLES LIKE 'therapist_profiles'")->rowCount() > 0;
    echo $profilesTableExists 
        ? "<p style='color:green'>✓ Therapist profiles table exists</p>" 
        : "<p style='color:red'>✗ Therapist profiles table does not exist</p>";
    
    if (!$userTableExists || !$profilesTableExists) {
        exit;
    }
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Error: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>Step 4: Check if users with therapist role exist</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'therapist'");
    $therapistCount = $stmt->fetchColumn();
    
    echo $therapistCount > 0
        ? "<p style='color:green'>✓ Found {$therapistCount} therapist users</p>"
        : "<p style='color:red'>✗ No therapist users found</p>";
    
    if ($therapistCount === 0) {
        echo "<p>Creating a test therapist user...</p>";
        
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password_hash, role, first_name, last_name, active, email_verified, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            'testtherapist', 
            '<EMAIL>', 
            password_hash('password123', PASSWORD_DEFAULT),
            'therapist',
            'Test',
            'Therapist',
            1,
            1
        ]);
        
        $userId = $pdo->lastInsertId();
        echo "<p>Created user with ID: {$userId}</p>";
    }
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Step 5: Check if therapist profiles exist</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM therapist_profiles");
    $profileCount = $stmt->fetchColumn();
    
    echo $profileCount > 0
        ? "<p style='color:green'>✓ Found {$profileCount} therapist profiles</p>"
        : "<p style='color:red'>✗ No therapist profiles found</p>";
    
    if ($profileCount === 0) {
        echo "<p>Creating a test therapist profile...</p>";
        
        // Get the first therapist user ID
        $stmt = $pdo->query("SELECT id FROM users WHERE role = 'therapist' LIMIT 1");
        $therapistId = $stmt->fetchColumn();
        
        if ($therapistId) {
            // First check if this user already has a profile
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM therapist_profiles WHERE user_id = ?");
            $stmt->execute([$therapistId]);
            if ($stmt->fetchColumn() === 0) {
                // Add the profile
                $stmt = $pdo->prepare("
                    INSERT INTO therapist_profiles 
                    (user_id, specialization, bio, experience_years, languages, status, is_approved, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $therapistId,
                    'Anxiety & Depression',
                    'Experienced therapist helping with anxiety and depression',
                    5,
                    'English',
                    'approved',
                    1
                ]);
                
                echo "<p>Created profile for user ID: {$therapistId}</p>";
            } else {
                echo "<p>User {$therapistId} already has a profile</p>";
            }
        } else {
            echo "<p style='color:red'>✗ No therapist users available to create a profile for</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Step 6: Check therapist profile columns</h2>";
try {
    $stmt = $pdo->query("DESCRIBE therapist_profiles");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>Therapist profile columns: " . implode(", ", $columns) . "</p>";
    
    // Check if the required columns exist
    $requiredColumns = ['status', 'is_approved', 'experience_years', 'languages'];
    $missingColumns = array_diff($requiredColumns, $columns);
    
    if (empty($missingColumns)) {
        echo "<p style='color:green'>✓ All required columns exist</p>";
    } else {
        echo "<p style='color:red'>✗ Missing columns: " . implode(", ", $missingColumns) . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Step 7: Test the exact query</h2>";
try {
    $query = "
        SELECT 
            u.id,
            u.first_name,
            u.last_name,
            u.email,
            tp.specialization,
            '../assets/images/default-avatar.png' as profile_image,
            '' as bio,
            COALESCE(tp.experience_years, 0) as experience_years,
            COALESCE(tp.languages, 'English') as languages,
            4.5 as rating,
            0 as review_count,
            0 as is_online
        FROM users u
        JOIN therapist_profiles tp ON u.id = tp.user_id
        WHERE u.role = 'therapist' AND (tp.status = 'approved' OR tp.is_approved = 1) AND u.active = 1
        ORDER BY u.first_name ASC, u.last_name ASC
    ";
    
    echo "<p>Executing query:</p>";
    echo "<pre>" . htmlspecialchars($query) . "</pre>";
    
    $stmt = $pdo->query($query);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color:green'>✓ Query executed successfully</p>";
    echo "<p>Found " . count($results) . " results:</p>";
    echo "<pre>" . print_r($results, true) . "</pre>";
    
    // Convert to JSON to check if any encoding issues
    $json = json_encode(['success' => true, 'data' => $results]);
    $jsonError = json_last_error();
    
    if ($jsonError !== JSON_ERROR_NONE) {
        echo "<p style='color:red'>✗ JSON encoding error: " . json_last_error_msg() . "</p>";
    } else {
        echo "<p style='color:green'>✓ JSON encoding successful</p>";
        echo "<pre>" . htmlspecialchars(substr($json, 0, 1000)) . (strlen($json) > 1000 ? '...' : '') . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color:red'>✗ Error: " . $e->getMessage() . "</p>";
} 
<?php
require_once '../config/config.php';
require_once '../auth/auth_middleware.php';

header('Content-Type: application/json');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    // Fetch all specializations from the database
    $stmt = $pdo->prepare("SELECT id, name FROM specializations ORDER BY name ASC");
    $stmt->execute();
    $specializations = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'specializations' => $specializations
    ]);
} catch (PDOException $e) {
    error_log($e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Failed to fetch specializations'
    ]);
}
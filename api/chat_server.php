<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../vendor/autoload.php';

use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;
use Ratchet\MessageComponentInterface;

class ChatServer implements MessageComponentInterface {
    protected $clients;
    protected $pdo;
    private $userConnections = [];

    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }

    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        echo "New connection! ({$conn->resourceId})\n";
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        $data = json_decode($msg, true);
        
        switch($data['type']) {
            case 'auth':
                $this->userConnections[$data['userId']] = $from;
                $this->updateUserStatus($data['userId'], true);
                break;
                
            case 'message':
                $this->handleNewMessage($from, $data);
                break;
                
            case 'typing':
                $this->broadcastTypingStatus($from, $data);
                break;
        }
    }

    protected function handleNewMessage($from, $data) {
        try {
            // Save message to database
            $stmt = $this->pdo->prepare("INSERT INTO messages (chat_id, sender_id, content, sent_time) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$data['chatId'], $data['senderId'], $data['content']]);
            
            // Update last message in chat
            $stmt = $this->pdo->prepare("UPDATE chats SET last_message = ?, last_message_time = NOW() WHERE id = ?");
            $stmt->execute([$data['content'], $data['chatId']]);
            
            // Get recipient connection
            if (isset($this->userConnections[$data['recipientId']])) {
                $recipientConn = $this->userConnections[$data['recipientId']];
                $recipientConn->send(json_encode([
                    'type' => 'message',
                    'chatId' => $data['chatId'],
                    'senderId' => $data['senderId'],
                    'content' => $data['content'],
                    'sentTime' => date('Y-m-d H:i:s')
                ]));
            }
        } catch (\Exception $e) {
            echo "Error: " . $e->getMessage() . "\n";
        }
    }

    protected function broadcastTypingStatus($from, $data) {
        if (isset($this->userConnections[$data['recipientId']])) {
            $recipientConn = $this->userConnections[$data['recipientId']];
            $recipientConn->send(json_encode([
                'type' => 'typing',
                'chatId' => $data['chatId'],
                'userId' => $data['userId'],
                'isTyping' => $data['isTyping']
            ]));
        }
    }

    protected function updateUserStatus($userId, $isOnline) {
        $stmt = $this->pdo->prepare("UPDATE users SET is_online = ?, last_active = NOW() WHERE id = ?");
        $stmt->execute([$isOnline ? 1 : 0, $userId]);
    }

    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);
        
        // Find and remove user connection
        $userId = array_search($conn, $this->userConnections);
        if ($userId !== false) {
            unset($this->userConnections[$userId]);
            $this->updateUserStatus($userId, false);
        }
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "Error: {$e->getMessage()}\n";
        $conn->close();
    }
}

$server = IoServer::factory(
    new HttpServer(
        new WsServer(
            new ChatServer()
        )
    ),
    8080
);

echo "WebSocket server started on port 8080\n";
$server->run();
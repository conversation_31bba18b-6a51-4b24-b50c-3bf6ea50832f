<?php
require_once '../config/config.php';
require_once '../includes/helpers.php';
require_once '../auth/auth_middleware.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
checkAuth();

$user_id = $_SESSION['user_id'];
$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    $action = $data['action'] ?? '';

    switch ($action) {
        case 'add':
            if (isset($data['description'])) {
                $stmt = $pdo->prepare("
                    INSERT INTO weekly_goals (user_id, goal_description, start_date, end_date)
                    VALUES (?, ?, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY))
                ");
                if ($stmt->execute([$user_id, $data['description']])) {
                    $response = ['success' => true, 'message' => 'Goal added successfully'];
                }
            }
            break;

        case 'update':
            if (isset($data['goal_id'], $data['status'])) {
                $stmt = $pdo->prepare("
                    UPDATE weekly_goals
                    SET status = ?
                    WHERE id = ? AND user_id = ?
                ");
                if ($stmt->execute([$data['status'], $data['goal_id'], $user_id])) {
                    $response = ['success' => true, 'message' => 'Goal status updated'];
                }
            }
            break;

        case 'delete':
            if (isset($data['goal_id'])) {
                $stmt = $pdo->prepare("DELETE FROM weekly_goals WHERE id = ? AND user_id = ?");
                if ($stmt->execute([$data['goal_id'], $user_id])) {
                    $response = ['success' => true, 'message' => 'Goal deleted successfully'];
                }
            }
            break;

        case 'list':
            $stmt = $pdo->prepare("
                SELECT *
                FROM weekly_goals
                WHERE user_id = ?
                AND start_date <= CURDATE()
                AND end_date >= CURDATE()
                ORDER BY created_at DESC
            ");
            $stmt->execute([$user_id]);
            $goals = $stmt->fetchAll();
            $response = ['success' => true, 'goals' => $goals];
            break;
    }
}

header('Content-Type: application/json');
echo json_encode($response);
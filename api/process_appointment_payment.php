<?php
require_once '../config/config.php';
require_once '../config/utilities.php';
require_once '../auth/auth_middleware.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
checkAuth();

// Initialize response array
$response = ['status' => 'error', 'message' => ''];

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = 'Invalid request method';
    header('Location: ../user/appointments.php');
    exit;
}

// Get and validate input data
$appointment_id = filter_input(INPUT_POST, 'appointment_id', FILTER_VALIDATE_INT);
$phoneNumber = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_STRING);
$amount = filter_input(INPUT_POST, 'amount', FILTER_VALIDATE_FLOAT);

// Validate appointment ID
if (!$appointment_id) {
    $_SESSION['error'] = 'Invalid appointment ID';
    header('Location: ../user/appointments.php');
    exit;
}

// Validate phone number format (Safaricom format)
if (!preg_match('/^(?:254|\+254|0)?(7[0-9]{8})$/', $phoneNumber)) {
    $_SESSION['error'] = 'Invalid phone number format';
    header('Location: ../user/appointments.php');
    exit;
}

// Standardize phone number to format required by M-Pesa API
$phoneNumber = preg_replace('/^(?:254|\+254|0)?/', '254', $phoneNumber);

// Get user ID from session
$user_id = $_SESSION['user_id'];

try {
    // Check if the appointment exists and belongs to the user
    $stmt = $pdo->prepare('SELECT a.*, u.first_name, u.last_name FROM appointments a 
                           JOIN users u ON a.therapist_id = u.id
                           WHERE a.id = ? AND a.user_id = ?');
    $stmt->execute([$appointment_id, $user_id]);
    $appointment = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$appointment) {
        throw new Exception('Appointment not found or does not belong to you');
    }
    
    // Use the appointment price from the database
    $amount = $appointment['price'];
    if ($amount <= 0) {
        throw new Exception('Invalid payment amount');
    }
    
    // Round the amount to whole number
    $amount = round($amount);

    // Create payment record
    $stmt = $pdo->prepare('INSERT INTO appointment_payments (
                              appointment_id, 
                              user_id, 
                              amount, 
                              payment_method, 
                              status, 
                              payment_date
                          ) VALUES (?, ?, ?, "mpesa", "pending", NOW())');
    $stmt->execute([$appointment_id, $user_id, $amount]);
    $payment_id = $pdo->lastInsertId();

    // Load M-Pesa configuration
    require_once '../config/mpesa_config.php';

    // M-Pesa credentials
    $mpesaConsumerKey = MPESA_CONSUMER_KEY;
    $mpesaConsumerSecret = MPESA_CONSUMER_SECRET;
    $mpesaPasskey = MPESA_PASSKEY;
    $mpesaShortcode = MPESA_SHORTCODE;
    $mpesaEnv = MPESA_ENVIRONMENT;

    // Generate M-Pesa access token
    $credentials = base64_encode($mpesaConsumerKey . ':' . $mpesaConsumerSecret);
    $url = $mpesaEnv === 'production' 
        ? 'https://api.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials'
        : 'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials';

    // Set proper SSL verification for sandbox environment
    $curl_opts = [
        CURLOPT_HTTPHEADER => ['Authorization: Basic ' . $credentials],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 30
    ];

    $ch = curl_init($url);
    curl_setopt_array($ch, $curl_opts);
    $response = curl_exec($ch);
    
    if ($response === false) {
        throw new Exception('Failed to connect to M-Pesa API: ' . curl_error($ch));
    }
    
    $result = json_decode($response);
    if (!$result) {
        throw new Exception('Invalid response from M-Pesa API');
    }
    curl_close($ch);

    if (!isset($result->access_token)) {
        throw new Exception('Failed to generate M-Pesa access token');
    }

    // Prepare STK Push
    $timestamp = date('YmdHis');
    $password = base64_encode($mpesaShortcode . $mpesaPasskey . $timestamp);
    $stkUrl = $mpesaEnv === 'production'
        ? 'https://api.safaricom.co.ke/mpesa/stkpush/v1/processrequest'
        : 'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest';

    $callbackUrl = MPESA_CALLBACK_URL;
    $description = 'Payment for session with Dr. ' . $appointment['first_name'] . ' ' . $appointment['last_name'];

    $stkData = [
        'BusinessShortCode' => $mpesaShortcode,
        'Password' => $password,
        'Timestamp' => $timestamp,
        'TransactionType' => 'CustomerPayBillOnline',
        'Amount' => $amount,
        'PartyA' => $phoneNumber,
        'PartyB' => $mpesaShortcode,
        'PhoneNumber' => $phoneNumber,
        'CallBackURL' => $callbackUrl,
        'AccountReference' => 'MindCare_A' . $appointment_id,
        'TransactionDesc' => $description
    ];

    $ch = curl_init($stkUrl);
    curl_setopt_array($ch, [
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $result->access_token,
            'Content-Type: application/json'
        ],
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($stkData),
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 30
    ]);
    
    // Log the request payload for debugging
    error_log('M-Pesa STK Push Request: ' . json_encode($stkData));
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    // Log the raw response
    error_log('M-Pesa STK Push Response: ' . $response);
    error_log('M-Pesa STK Push HTTP Code: ' . $httpCode);

    if ($response === false) {
        throw new Exception('Failed to initiate M-Pesa payment. Curl error: ' . $curlError);
    }
    
    $stkResult = json_decode($response);
    if (!$stkResult) {
        throw new Exception('Invalid JSON response from M-Pesa STK push: ' . $response);
    }

    // Check for API error response
    if (isset($stkResult->errorCode) || isset($stkResult->errorMessage)) {
        $errorMessage = isset($stkResult->errorMessage) ? $stkResult->errorMessage : 'Unknown API error';
        error_log('M-Pesa API Error: ' . $errorMessage);
        throw new Exception('M-Pesa API Error: ' . $errorMessage);
    }

    if (!isset($stkResult->MerchantRequestID) || !isset($stkResult->CheckoutRequestID)) {
        error_log('M-Pesa Invalid Response Structure: ' . json_encode($stkResult));
        throw new Exception('Invalid response structure from M-Pesa API');
    }

    $merchantRequestId = $stkResult->MerchantRequestID;
    $checkoutRequestId = $stkResult->CheckoutRequestID;

    // Update payment record with request IDs
    $stmt = $pdo->prepare('UPDATE appointment_payments SET 
                             transaction_id = ?, 
                             merchant_request_id = ? 
                           WHERE id = ?');
    $stmt->execute([$checkoutRequestId, $merchantRequestId, $payment_id]);

    // Set success message
    $_SESSION['success'] = 'Payment initiated successfully. Please check your phone for the M-Pesa payment prompt.';
    
    // Redirect back to appointments page
    header('Location: ../user/appointments.php');
    exit;

} catch (Exception $e) {
    $_SESSION['error'] = 'Failed to process payment: ' . $e->getMessage();
    header('Location: ../user/appointments.php');
    exit;
} 
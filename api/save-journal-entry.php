<?php
require_once '../config/config.php';
require_once '../auth/auth_middleware.php';

header('Content-Type: application/json');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

checkAuth();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

$user_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['content']) || empty(trim($data['content']))) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Journal content is required']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Insert journal entry
    $stmt = $pdo->prepare("INSERT INTO journal_entries (user_id, content) VALUES (?, ?)");
    $stmt->execute([$user_id, $data['content']]);
    $journal_entry_id = $pdo->lastInsertId();

    // Insert mood entry if provided
    if (isset($data['mood_score']) && isset($data['mood_description'])) {
        $stmt = $pdo->prepare("INSERT INTO mood_entries (user_id, journal_entry_id, mood_score, mood_description) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $user_id,
            $journal_entry_id,
            $data['mood_score'],
            $data['mood_description']
        ]);
    }

    $pdo->commit();

    echo json_encode([
        'status' => 'success',
        'message' => 'Journal entry saved successfully',
        'entry_id' => $journal_entry_id
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to save journal entry'
    ]);
}
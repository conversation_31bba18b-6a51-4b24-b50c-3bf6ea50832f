<?php
require_once '../config/config.php';
require_once '../auth/auth_middleware.php';
require_once '../api/notification_handler.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Validate input
$therapist_id = filter_input(INPUT_POST, 'therapist_id', FILTER_VALIDATE_INT);
$appointment_date = filter_input(INPUT_POST, 'appointment_date', FILTER_SANITIZE_STRING);
$appointment_type = filter_input(INPUT_POST, 'appointment_type', FILTER_SANITIZE_STRING);
$user_id = $_SESSION['user_id'];

if (!$therapist_id || !$appointment_date || !$appointment_type) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

// Validate appointment type
$valid_types = ['initial', 'follow-up', 'extended'];
if (!in_array($appointment_type, $valid_types)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid appointment type']);
    exit;
}

// Get appointment duration and price based on type
$duration_prices = [
    'initial' => ['duration' => 60, 'price' => 100],
    'follow-up' => ['duration' => 45, 'price' => 80],
    'extended' => ['duration' => 90, 'price' => 150]
];

$duration = $duration_prices[$appointment_type]['duration'];
$price = $duration_prices[$appointment_type]['price'];

try {
    // Validate therapist exists and is active
    $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ? AND role = 'therapist' AND active = TRUE");
    $stmt->execute([$therapist_id]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Therapist not found or inactive']);
        exit;
    }

    // Check if the slot is still available
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM appointments 
        WHERE therapist_id = ? 
        AND appointment_date = ? 
        AND status != 'cancelled'
    ");
    $stmt->execute([$therapist_id, $appointment_date]);
    
    if ($stmt->fetchColumn() > 0) {
        http_response_code(409);
        echo json_encode(['success' => false, 'message' => 'This time slot is no longer available']);
        exit;
    }

    // Check if user has any existing appointments with this therapist on the same day
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM appointments 
        WHERE user_id = ? 
        AND therapist_id = ? 
        AND DATE(appointment_date) = DATE(?) 
        AND status != 'cancelled'
    ");
    $stmt->execute([$user_id, $therapist_id, $appointment_date]);
    
    if ($stmt->fetchColumn() > 0) {
        http_response_code(409);
        echo json_encode(['success' => false, 'message' => 'You already have an appointment with this therapist on this day']);
        exit;
    }

    // Begin transaction
    $pdo->beginTransaction();

    // Insert the appointment
    $stmt = $pdo->prepare("
        INSERT INTO appointments 
        (user_id, therapist_id, appointment_date, duration, price, appointment_type, status, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, 'scheduled', NOW())
    ");
    
    $stmt->execute([
        $user_id,
        $therapist_id,
        $appointment_date,
        $duration,
        $price,
        $appointment_type
    ]);

    $appointment_id = $pdo->lastInsertId();

    // Create notifications
    $notificationHandler = new NotificationHandler($pdo);
    
    // Get user and therapist names for notifications
    $stmt = $pdo->prepare("
        SELECT u.first_name as user_first_name, u.last_name as user_last_name,
               t.first_name as therapist_first_name, t.last_name as therapist_last_name
        FROM users u
        CROSS JOIN users t
        WHERE u.id = ? AND t.id = ?
    ");
    $stmt->execute([$user_id, $therapist_id]);
    $names = $stmt->fetch(PDO::FETCH_ASSOC);

    // Create notification for user
    $userMessage = "Your {$appointment_type} appointment with Dr. {$names['therapist_first_name']} {$names['therapist_last_name']} has been scheduled for " . 
                  date('F j, Y g:i A', strtotime($appointment_date));
    $notificationHandler->createNotification($user_id, 'appointment_scheduled', $userMessage, $appointment_id);

    // Create notification for therapist
    $therapistMessage = "New {$appointment_type} appointment scheduled with {$names['user_first_name']} {$names['user_last_name']} for " . 
                       date('F j, Y g:i A', strtotime($appointment_date));
    $notificationHandler->createNotification($therapist_id, 'appointment_scheduled', $therapistMessage, $appointment_id);

    // Send email notifications
    $stmt = $pdo->prepare("SELECT email FROM users WHERE id IN (?, ?)");
    $stmt->execute([$user_id, $therapist_id]);
    $emails = $stmt->fetchAll(PDO::FETCH_COLUMN);

    foreach ($emails as $email) {
        $subject = "Appointment Scheduled - MindCare";
        $message = "A new appointment has been scheduled:\n\n" .
                  "Type: {$appointment_type}\n" .
                  "Date: " . date('F j, Y g:i A', strtotime($appointment_date)) . "\n" .
                  "Duration: {$duration} minutes\n" .
                  "Price: $" . number_format($price, 2) . "\n\n" .
                  "Please log in to your account for more details.";
        
        mail($email, $subject, $message);
    }

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Appointment booked successfully',
        'appointment_id' => $appointment_id,
        'details' => [
            'type' => $appointment_type,
            'date' => $appointment_date,
            'duration' => $duration,
            'price' => $price
        ]
    ]);

} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error booking appointment']);
    error_log('Error in book_appointment.php: ' . $e->getMessage());
}
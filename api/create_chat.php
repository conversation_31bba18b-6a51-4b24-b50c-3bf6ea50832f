<?php
require_once '../config/config.php';
require_once '../auth/auth_middleware.php';

header('Content-Type: application/json');

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$user_id = $_SESSION['user_id'];
$therapist_id = $data['therapist_id'] ?? null;

if (!$therapist_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Therapist ID is required']);
    exit;
}

try {
    // Check if chat already exists
    $stmt = $pdo->prepare("SELECT id FROM chats WHERE user_id = ? AND therapist_id = ?");
    $stmt->execute([$user_id, $therapist_id]);
    $existing_chat = $stmt->fetch();

    if ($existing_chat) {
        echo json_encode([
            'success' => true,
            'chat_id' => $existing_chat['id'],
            'message' => 'Chat already exists'
        ]);
        exit;
    }

    // Create new chat
    $stmt = $pdo->prepare("INSERT INTO chats (user_id, therapist_id, created_at) VALUES (?, ?, NOW())");
    $stmt->execute([$user_id, $therapist_id]);
    $chat_id = $pdo->lastInsertId();

    // Create welcome message
    $stmt = $pdo->prepare("INSERT INTO messages (chat_id, sender_id, content, sent_time) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$chat_id, $therapist_id, 'Hello! How can I help you today?']);

    echo json_encode([
        'success' => true,
        'chat_id' => $chat_id,
        'message' => 'Chat created successfully'
    ]);
} catch (PDOException $e) {
    error_log($e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to create chat'
    ]);
}
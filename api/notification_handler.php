<?php
require_once '../config/config.php';

class NotificationHandler {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function createNotification($userId, $type, $message, $relatedId = null) {
        try {
            $stmt = $this->pdo->prepare("INSERT INTO notifications (user_id, type, message, related_id, created_at) VALUES (?, ?, ?, ?, NOW())");
            return $stmt->execute([$userId, $type, $message, $relatedId]);
        } catch (PDOException $e) {
            error_log('Error creating notification: ' . $e->getMessage());
            return false;
        }
    }
    
    public function getUnreadNotifications($userId) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM notifications WHERE user_id = ? AND is_read = 0 ORDER BY created_at DESC");
            $stmt->execute([$userId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log('Error fetching notifications: ' . $e->getMessage());
            return [];
        }
    }
    
    public function markAsRead($notificationId, $userId) {
        try {
            $stmt = $this->pdo->prepare("UPDATE notifications SET is_read = 1 WHERE id = ? AND user_id = ?");
            return $stmt->execute([$notificationId, $userId]);
        } catch (PDOException $e) {
            error_log('Error marking notification as read: ' . $e->getMessage());
            return false;
        }
    }
    
    public function handleAppointmentNotification($appointmentId, $status) {
        try {
            // Get appointment details
            $stmt = $this->pdo->prepare("SELECT a.*, u.id as user_id, u.first_name as user_name, 
                                      t.id as therapist_id, t.first_name as therapist_name 
                                      FROM appointments a 
                                      JOIN users u ON a.user_id = u.id 
                                      JOIN users t ON a.therapist_id = t.id 
                                      WHERE a.id = ?");
            $stmt->execute([$appointmentId]);
            $appointment = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$appointment) return false;
            
            // Create appropriate notifications based on status
            switch($status) {
                case 'confirmed':
                    $userMsg = "Your appointment with {$appointment['therapist_name']} has been confirmed.";
                    $therapistMsg = "You have confirmed an appointment with {$appointment['user_name']}.";
                    break;
                    
                case 'cancelled':
                    $userMsg = "Your appointment with {$appointment['therapist_name']} has been cancelled.";
                    $therapistMsg = "An appointment with {$appointment['user_name']} has been cancelled.";
                    break;
                    
                case 'completed':
                    $userMsg = "Your appointment with {$appointment['therapist_name']} has been marked as completed.";
                    $therapistMsg = "Your appointment with {$appointment['user_name']} has been marked as completed.";
                    break;
                    
                default:
                    return false;
            }
            
            // Create notifications for both user and therapist
            $this->createNotification($appointment['user_id'], 'appointment', $userMsg, $appointmentId);
            $this->createNotification($appointment['therapist_id'], 'appointment', $therapistMsg, $appointmentId);
            
            return true;
        } catch (PDOException $e) {
            error_log('Error handling appointment notification: ' . $e->getMessage());
            return false;
        }
    }
}
<?php
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $baseDir = dirname($_SERVER['PHP_SELF']);
    return $protocol . $host . $baseDir . '/';
}

function isAuthenticated() {
    return isset($_SESSION['user_id']) && !isset($_SESSION['is_guest']);
}

function requireRole($allowedRoles) {
    if (!isset($_SESSION['role']) || !in_array($_SESSION['role'], $allowedRoles)) {
        header('Location: ' . getBaseUrl() . 'login.php');
        exit();
    }
}

function sanitizeOutput($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

function getenv_default($key, $default = null) {
    $value = getenv($key);
    return $value !== false ? $value : $default;
}
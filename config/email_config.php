<?php
require_once __DIR__ . '/env.php';

// Email configuration
define('SMTP_HOST', getenv('SMTP_HOST') ?: 'smtp.mailersend.net');
define('SMTP_PORT', getenv('SMTP_PORT') ?: 587);
define('SMTP_USERNAME', getenv('SMTP_USERNAME') ?: '');
define('SMTP_PASSWORD', getenv('SMTP_PASSWORD') ?: '');
define('SMTP_ENCRYPTION', getenv('SMTP_ENCRYPTION') ?: 'tls');
define('SMTP_FROM_EMAIL', getenv('SMTP_FROM_EMAIL') ?: '');
define('SMTP_FROM_NAME', getenv('SMTP_FROM_NAME') ?: 'MindCare');

// Email error logging function
function logEmailError($error, $context = []) {
    $logMessage = date('Y-m-d H:i:s') . " - Email Error: " . $error;
    if (!empty($context)) {
        $logMessage .= "\nContext: " . json_encode($context);
    }
    error_log($logMessage . "\n", 3, __DIR__ . '/../logs/email_errors.log');
}

// Validate email configuration
function validateEmailConfig() {
    $required = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USERNAME', 'SMTP_PASSWORD', 'SMTP_FROM_EMAIL'];
    $missing = [];
    
    foreach ($required as $key) {
        if (empty(constant($key))) {
            $missing[] = $key;
        }
    }
    
    if (!empty($missing)) {
        logEmailError('Missing required email configuration', ['missing' => $missing]);
        return false;
    }
    
    return true;
}

// Initialize email configuration validation
if (!validateEmailConfig()) {
    error_log('Email configuration is incomplete. Please check the logs for details.');
}
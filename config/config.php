<?php
// Load utilities first
require_once __DIR__ . '/utilities.php';

// Load environment variables
require_once __DIR__ . '/env.php';

// Database configuration
define('DB_HOST', getenv_default('DB_HOST', 'localhost'));
define('DB_NAME', getenv_default('DB_NAME', 'mindcare'));
define('DB_USER', getenv_default('DB_USER', 'root'));
define('DB_PASS', getenv_default('DB_PASS', ''));

// Create PDO connection
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Session configuration
if (session_status() === PHP_SESSION_NONE) {
    // Check if we're in the admin section
    $isAdminSection = strpos($_SERVER['PHP_SELF'], '/admin/') !== false;
    
    if ($isAdminSection) {
        session_name('admin_session');
    } else {
        session_name('mindcare_session');
    }
    
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    session_start();
}

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>

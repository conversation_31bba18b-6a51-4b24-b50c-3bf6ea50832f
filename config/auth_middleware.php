<?php
require_once __DIR__ . '/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

class AuthMiddleware {
    private $pdo;
    private $baseUrl;
    private $publicPages = ['login.php', 'register.php', 'forgot-password.php', 'index.php', 'about.php', 'contact.php'];

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->baseUrl = getBaseUrl();
    }

    private function isPublicPage($script) {
        return in_array($script, $this->publicPages);
    }

    public function authenticateUser() {
        $currentScript = basename($_SERVER['SCRIPT_NAME']);
        
        // Allow access to public pages without authentication
        if ($this->isPublicPage($currentScript)) {
            return true;
        }

        // Check if user is logged in
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['role'])) {
            $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
            $_SESSION['error'] = 'Please log in to access this page.';
            header('Location: ' . $this->baseUrl . 'login.php');
            exit();
        }

        // Check user role
        if ($_SESSION['role'] !== 'user') {
            $_SESSION['error'] = 'Access denied. User privileges required.';
            header('Location: ' . $this->baseUrl . 'login.php');
            exit();
        }

        return true;
    }

    public function authenticateTherapist() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || !isset($_SESSION['therapist_id'])) {
            $_SESSION['error'] = 'Please log in to access this page.';
            header('Location: ' . $this->baseUrl . 'therapist/login.php');
            exit();
        }

        if ($_SESSION['role'] !== 'therapist') {
            $_SESSION['error'] = 'Access denied. Therapist privileges required.';
            header('Location: ' . $this->baseUrl . 'therapist/login.php');
            exit();
        }

        // Verify therapist approval status
        $stmt = $this->pdo->prepare("SELECT status FROM therapists WHERE id = ?");
        $stmt->execute([$_SESSION['therapist_id']]);
        $therapist = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$therapist || $therapist['status'] !== 'approved') {
            session_destroy();
            $_SESSION['error'] = 'Your therapist account is not approved.';
            header('Location: ' . $this->baseUrl . 'therapist/login.php');
            exit();
        }

        return true;
    }

    public function authenticateAdmin() {
        if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_role'])) {
            $_SESSION['error'] = 'Please log in to access this page.';
            header('Location: ' . $this->baseUrl . 'admin/login.php');
            exit();
        }

        if ($_SESSION['admin_role'] !== 'admin') {
            $_SESSION['error'] = 'Access denied. Admin privileges required.';
            header('Location: ' . $this->baseUrl . 'admin/login.php');
            exit();
        }

        return true;
    }

    public function logout($role = 'user') {
        switch ($role) {
            case 'admin':
                unset($_SESSION['admin_id']);
                unset($_SESSION['admin_role']);
                unset($_SESSION['admin_name']);
                header('Location: ' . $this->baseUrl . 'admin/login.php');
                break;
            case 'therapist':
                unset($_SESSION['user_id']);
                unset($_SESSION['role']);
                unset($_SESSION['therapist_id']);
                header('Location: ' . $this->baseUrl . 'therapist/login.php');
                break;
            default:
                unset($_SESSION['user_id']);
                unset($_SESSION['role']);
                header('Location: ' . $this->baseUrl . 'login.php');
        }
        session_destroy();
        exit();
    }
}
<?php
// Load environment variables
if (!function_exists('getenv_default')) {
    function getenv_default($key, $default = null) {
        $value = getenv($key);
        return $value !== false ? $value : $default;
    }
}

// M-Pesa API Configuration
define('MPESA_ENVIRONMENT', getenv_default('MPESA_ENV', 'sandbox'));

// API Credentials
define('MPESA_CONSUMER_KEY', getenv_default('MPESA_CONSUMER_KEY'));
define('MPESA_CONSUMER_SECRET', getenv_default('MPESA_CONSUMER_SECRET'));

// Business Details
define('MPESA_SHORTCODE', getenv_default('MPESA_SHORTCODE', '174379'));
define('MPESA_PASSKEY', getenv_default('MPESA_PASSKEY'));

// Callback URLs
define('MPESA_CALLBACK_URL', getenv_default('MPESA_CALLBACK_URL'));

// Validate required credentials
if (!MPESA_CONSUMER_KEY || !MPESA_CONSUMER_SECRET || !MPESA_PASSKEY) {
    error_log('M-Pesa API credentials are not properly configured: ' . 
        (!MPESA_CONSUMER_KEY ? 'Missing Consumer Key, ' : '') . 
        (!MPESA_CONSUMER_SECRET ? 'Missing Consumer Secret, ' : '') . 
        (!MPESA_PASSKEY ? 'Missing Passkey' : ''));
    throw new Exception('M-Pesa API credentials are not properly configured. Please check your environment variables.');
}

// Validate callback URL
if (!MPESA_CALLBACK_URL) {
    error_log('M-Pesa callback URL is not configured');
    throw new Exception('M-Pesa callback URL is not properly configured. Please check your environment variables.');
}

// Log environment configuration
error_log('M-Pesa Configuration Loaded - Environment: ' . MPESA_ENVIRONMENT);


// Transaction Type
define('MPESA_TRANSACTION_TYPE', 'CustomerPayBillOnline');

// Account Reference
define('MPESA_ACCOUNT_REFERENCE', 'MindCare');

// Transaction Description
define('MPESA_TRANSACTION_DESC', 'MindCare Subscription Payment');

// API Endpoints
if (MPESA_ENVIRONMENT === 'sandbox') {
    define('MPESA_AUTH_URL', 'https://sandbox.safaricom.co.ke/oauth/v1/generate');
    define('MPESA_STK_URL', 'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest');
} else {
    define('MPESA_AUTH_URL', 'https://api.safaricom.co.ke/oauth/v1/generate');
    define('MPESA_STK_URL', 'https://api.safaricom.co.ke/mpesa/stkpush/v1/processrequest');
}